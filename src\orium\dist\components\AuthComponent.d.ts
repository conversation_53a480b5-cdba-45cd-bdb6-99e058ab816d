/**
 * Authentication component for Orium
 * Handles sign-up, sign-in, OAuth, and MFA
 */
import type { OriumUser } from '../types';
export interface AuthState {
    user: OriumUser | null;
    isLoading: boolean;
    error: string | null;
    needsMFA: boolean;
    mfaChallenge: any | null;
}
export declare class AuthComponent {
    private state;
    private onStateChangeCallback?;
    constructor();
    /**
     * Initialize authentication state
     */
    private initializeAuth;
    /**
     * Sign up with email and password
     */
    signUp(email: string, password: string, name?: string): Promise<void>;
    /**
     * Sign in with email and password
     */
    signIn(email: string, password: string): Promise<void>;
    /**
     * Sign in with OAuth provider
     */
    signInWithOAuth(provider: 'google' | 'github'): Promise<void>;
    /**
     * Sign in with passwordless link
     */
    signInWithMagicLink(email: string): Promise<void>;
    /**
     * Sign out
     */
    signOut(): Promise<void>;
    /**
     * Enable MFA
     */
    enableMFA(): Promise<{
        qrCode?: string;
        secret?: string;
    }>;
    /**
     * Verify MFA code
     */
    verifyMFA(factorId: string, challengeId: string, code: string): Promise<void>;
    /**
     * Get current authentication state
     */
    getState(): AuthState;
    /**
     * Set state change callback
     */
    onStateChange(callback: (state: AuthState) => void): void;
    /**
     * Update state and notify listeners
     */
    private setState;
    /**
     * Check if user is authenticated
     */
    isAuthenticated(): boolean;
    /**
     * Get current user
     */
    getCurrentUser(): OriumUser | null;
    /**
     * Handle OAuth callback
     */
    handleOAuthCallback(): Promise<void>;
}
export declare const authComponent: AuthComponent;
//# sourceMappingURL=AuthComponent.d.ts.map