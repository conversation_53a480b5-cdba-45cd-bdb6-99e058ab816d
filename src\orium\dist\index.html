<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orium - Collaborative Code Editor</title>
    <meta name="description" content="Secure AI-powered real-time collaborative code editor">
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🚀</text></svg>">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Monaco Editor -->
    <link rel="stylesheet" data-name="vs/editor/editor.main" href="https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/editor/editor.main.css">
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Base Styles -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        html, body {
            height: 100%;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1e1e1e;
            color: #ffffff;
            overflow: hidden;
        }
        
        #orium-app {
            width: 100%;
            height: 100vh;
            position: relative;
        }
        
        /* Loading animation */
        .loading-dots {
            display: inline-block;
            position: relative;
            width: 80px;
            height: 80px;
        }
        
        .loading-dots div {
            position: absolute;
            top: 33px;
            width: 13px;
            height: 13px;
            border-radius: 50%;
            background: #007ACC;
            animation-timing-function: cubic-bezier(0, 1, 1, 0);
        }
        
        .loading-dots div:nth-child(1) {
            left: 8px;
            animation: loading-dots1 0.6s infinite;
        }
        
        .loading-dots div:nth-child(2) {
            left: 8px;
            animation: loading-dots2 0.6s infinite;
        }
        
        .loading-dots div:nth-child(3) {
            left: 32px;
            animation: loading-dots2 0.6s infinite;
        }
        
        .loading-dots div:nth-child(4) {
            left: 56px;
            animation: loading-dots3 0.6s infinite;
        }
        
        @keyframes loading-dots1 {
            0% { transform: scale(0); }
            100% { transform: scale(1); }
        }
        
        @keyframes loading-dots3 {
            0% { transform: scale(1); }
            100% { transform: scale(0); }
        }
        
        @keyframes loading-dots2 {
            0% { transform: translate(0, 0); }
            100% { transform: translate(24px, 0); }
        }
        
        /* Splash screen */
        .splash-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            transition: opacity 0.5s ease-out;
        }
        
        .splash-screen.fade-out {
            opacity: 0;
            pointer-events: none;
        }
        
        .splash-logo {
            font-size: 4rem;
            font-weight: 700;
            background: linear-gradient(135deg, #007ACC 0%, #00A8FF 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .splash-tagline {
            font-size: 1.2rem;
            color: #cccccc;
            margin-bottom: 2rem;
            text-align: center;
        }
        
        /* Error styles */
        .error-boundary {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100vh;
            padding: 2rem;
            text-align: center;
        }
        
        .error-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        
        .error-title {
            font-size: 2rem;
            font-weight: 600;
            color: #ff4444;
            margin-bottom: 1rem;
        }
        
        .error-message {
            font-size: 1.1rem;
            color: #cccccc;
            margin-bottom: 2rem;
            max-width: 600px;
        }
        
        .error-actions {
            display: flex;
            gap: 1rem;
        }
        
        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn-primary {
            background: #007ACC;
            color: #ffffff;
        }
        
        .btn-primary:hover {
            background: #005a9e;
        }
        
        .btn-secondary {
            background: #404040;
            color: #ffffff;
        }
        
        .btn-secondary:hover {
            background: #505050;
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .splash-logo {
                font-size: 3rem;
            }
            
            .splash-tagline {
                font-size: 1rem;
                padding: 0 1rem;
            }
            
            .error-title {
                font-size: 1.5rem;
            }
            
            .error-message {
                font-size: 1rem;
            }
            
            .error-actions {
                flex-direction: column;
                width: 100%;
                max-width: 300px;
            }
        }
        
        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            .loading-dots div {
                animation: none;
            }
            
            .splash-screen {
                transition: none;
            }
        }
        
        /* High contrast mode */
        @media (prefers-contrast: high) {
            .splash-logo {
                background: #ffffff;
                -webkit-background-clip: text;
                -webkit-text-fill-color: transparent;
                background-clip: text;
            }
        }
    </style>
</head>
<body>
    <!-- Splash Screen -->
    <div id="splash-screen" class="splash-screen">
        <div class="splash-logo">Orium</div>
        <div class="splash-tagline">Secure AI-Powered Collaborative Code Editor</div>
        <div class="loading-dots">
            <div></div>
            <div></div>
            <div></div>
            <div></div>
        </div>
    </div>
    
    <!-- Main Application Container -->
    <div id="orium-app"></div>
    
    <!-- Error Boundary Template -->
    <template id="error-boundary-template">
        <div class="error-boundary">
            <div class="error-icon">⚠️</div>
            <h1 class="error-title">Something went wrong</h1>
            <p class="error-message">
                We're sorry, but something unexpected happened. Please try refreshing the page or contact support if the problem persists.
            </p>
            <div class="error-actions">
                <button class="btn btn-primary" onclick="location.reload()">Refresh Page</button>
                <a href="mailto:<EMAIL>" class="btn btn-secondary">Contact Support</a>
            </div>
        </div>
    </template>
    
    <!-- Monaco Editor -->
    <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs/loader.js"></script>
    
    <!-- Application Scripts -->
    <script type="module">
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            showErrorBoundary(event.error.message);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            showErrorBoundary('An unexpected error occurred');
        });
        
        function showErrorBoundary(message) {
            const template = document.getElementById('error-boundary-template');
            const errorBoundary = template.content.cloneNode(true);
            const messageElement = errorBoundary.querySelector('.error-message');
            if (messageElement) {
                messageElement.textContent = message;
            }
            
            document.body.innerHTML = '';
            document.body.appendChild(errorBoundary);
        }
        
        // Initialize Monaco Editor
        require.config({ 
            paths: { 
                'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.45.0/min/vs' 
            } 
        });
        
        // Application initialization
        async function initializeApp() {
            try {
                // Wait for Monaco to load
                await new Promise((resolve) => {
                    require(['vs/editor/editor.main'], resolve);
                });
                
                // Import and initialize Orium
                const { OriumApp } = await import('./OriumApp.js');
                
                const container = document.getElementById('orium-app');
                if (!container) {
                    throw new Error('Application container not found');
                }
                
                // Create Orium app instance
                const app = new OriumApp(container, {
                    websocketUrl: 'ws://localhost:1234',
                    enableAnalytics: false,
                    theme: 'dark'
                });
                
                // Make globally accessible
                window.oriumApp = app;
                
                // Hide splash screen
                setTimeout(() => {
                    const splashScreen = document.getElementById('splash-screen');
                    if (splashScreen) {
                        splashScreen.classList.add('fade-out');
                        setTimeout(() => {
                            splashScreen.remove();
                        }, 500);
                    }
                }, 2000);
                
                console.log('Orium application initialized successfully');
                
            } catch (error) {
                console.error('Failed to initialize application:', error);
                showErrorBoundary('Failed to initialize the application. Please refresh the page and try again.');
            }
        }
        
        // Start the application
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }
        
        // Service Worker registration (for PWA capabilities)
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // Performance monitoring
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    console.log('Page load time:', perfData.loadEventEnd - perfData.loadEventStart, 'ms');
                }, 0);
            });
        }
    </script>
    
    <!-- Analytics (placeholder) -->
    <script>
        // Analytics initialization would go here
        // For privacy, this is disabled by default
        if (window.oriumConfig?.enableAnalytics) {
            // Initialize analytics
        }
    </script>
</body>
</html>
