{"version": 3, "file": "collaborationService.js", "sourceRoot": "", "sources": ["../../services/collaborationService.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,KAAK,CAAC,MAAM,KAAK,CAAC;AACzB,OAAO,EAAE,iBAAiB,EAAE,MAAM,aAAa,CAAC;AAChD,OAAO,EAAE,aAAa,EAAE,MAAM,UAAU,CAAC;AAEzC,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAiBnD,MAAM,OAAO,oBAAoB;IACvB,IAAI,CAAQ;IACZ,QAAQ,GAA6B,IAAI,CAAC;IAC1C,OAAO,GAAyB,IAAI,CAAC;IACrC,SAAS,GAAkB,IAAI,CAAC;IAChC,WAAW,GAA6B,IAAI,CAAC;IAC7C,KAAK,GAAmC,IAAI,GAAG,EAAE,CAAC;IAClD,sBAAsB,GAA0B,IAAI,CAAC;IAE7D,kBAAkB;IACV,sBAAsB,CAAwC;IAC9D,uBAAuB,CAAsE;IAC7F,0BAA0B,CAA2H;IAE7J;QACE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CAAC,SAAiB,EAAE,eAAuB,qBAAqB;QACrF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,wBAAwB;QACxB,MAAM,IAAI,GAAG,eAAe,CAAC,cAAc,EAAE,CAAC;QAC9C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;QAC5C,CAAC;QAED,IAAI,CAAC,WAAW,GAAG;YACjB,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,WAAW;YAC3D,KAAK,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;SACvC,CAAC;QAEF,2CAA2C;QAC3C,MAAM,KAAK,GAAG,GAAG,YAAY,uBAAuB,SAAS,WAAW,IAAI,CAAC,EAAE,aAAa,kBAAkB,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;QACxI,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAiB,CAAC,KAAK,EAAE,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAEnE,mCAAmC;QACnC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAErE,+BAA+B;QAC/B,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5E,gCAAgC;QAChC,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAEtF,yBAAyB;QACzB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,OAAO,CAAC,GAAG,CAAC,0CAA0C,SAAS,EAAE,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,YAA0C;QACnD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACnB,MAAM,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC;QACnD,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAE1C,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAC9B,KAAK,EACL,YAAY,CAAC,QAAQ,EAAG,EACxB,IAAI,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,EACvB,IAAI,CAAC,QAAQ,CAAC,SAAS,CACxB,CAAC;QAEF,0CAA0C;QAC1C,YAAY,CAAC,yBAAyB,CAAC,CAAC,CAAC,EAAE,EAAE;YAC3C,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC;QAEH,YAAY,CAAC,0BAA0B,CAAC,CAAC,CAAC,EAAE,EAAE;YAC5C,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,UAAU;QACR,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChC,aAAa,CAAC,IAAI,CAAC,sBAAsB,CAAC,CAAC;YAC3C,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC;QACrC,CAAC;QAED,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;YAClB,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YACxB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACvB,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QACnB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;QACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;QAExB,OAAO,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,OAAe;QAChC,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;QAC9B,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC3B,CAAC;IAED;;OAEG;IACH,iBAAiB;QACf,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,QAA8C;QAC3D,IAAI,CAAC,sBAAsB,GAAG,QAAQ,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAA4E;QAC1F,IAAI,CAAC,uBAAuB,GAAG,QAAQ,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,QAAiI;QAClJ,IAAI,CAAC,0BAA0B,GAAG,QAAQ,CAAC;IAC7C,CAAC;IAED;;OAEG;IACK,qBAAqB;QAC3B,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAE3B,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;QACnD,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;QAEnB,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;YACjC,IAAI,KAAK,CAAC,IAAI,IAAI,QAAQ,KAAK,IAAI,CAAC,QAAS,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACjE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,4BAA4B;QAC5B,IAAI,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChC,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,sBAAsB,CAAC,KAAmB;QAChD,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAEvC,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACrB,KAAK,SAAS;oBACZ,OAAO,CAAC,GAAG,CAAC,qBAAqB,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,cAAc,eAAe,CAAC,CAAC;oBAC7F,MAAM;gBAER,KAAK,aAAa;oBAChB,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,QAAQ,qBAAqB,CAAC,CAAC;oBAC3D,MAAM;gBAER,KAAK,WAAW;oBACd,OAAO,CAAC,GAAG,CAAC,QAAQ,OAAO,CAAC,QAAQ,mBAAmB,CAAC,CAAC;oBACzD,MAAM;gBAER,KAAK,cAAc;oBACjB,6DAA6D;oBAC7D,MAAM;gBAER,KAAK,eAAe;oBAClB,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;wBACjC,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC;oBAC/D,CAAC;oBACD,MAAM;gBAER,KAAK,kBAAkB;oBACrB,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;wBACpC,IAAI,CAAC,0BAA0B,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC;oBACrE,CAAC;oBACD,MAAM;gBAER;oBACE,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;YACvD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,QAAgD;QACzE,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEhD,MAAM,MAAM,GAAG;YACb,IAAI,EAAE,QAAQ,CAAC,UAAU;YACzB,MAAM,EAAE,QAAQ,CAAC,MAAM;SACxB,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;QACjC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAErE,8BAA8B;QAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,6BAA6B;QAC7B,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACjC,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;QAC5D,CAAC;IACH,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,SAAqG;QACjI,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEhD,MAAM,aAAa,GAAG;YACpB,KAAK,EAAE;gBACL,IAAI,EAAE,SAAS,CAAC,eAAe;gBAC/B,MAAM,EAAE,SAAS,CAAC,WAAW;aAC9B;YACD,GAAG,EAAE;gBACH,IAAI,EAAE,SAAS,CAAC,aAAa;gBAC7B,MAAM,EAAE,SAAS,CAAC,SAAS;aAC5B;SACF,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,aAAa,CAAC;QAC3C,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,kBAAkB,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAErE,8BAA8B;QAC9B,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAE9B,gCAAgC;QAChC,IAAI,IAAI,CAAC,0BAA0B,EAAE,CAAC;YACpC,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,sBAAsB;QAClC,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO;QAEjD,MAAM,QAAQ,GAA0B;YACtC,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM;YACxC,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS;YACrC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK;SAC9B,CAAC;QAEF,MAAM,eAAe,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;IACjE,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,IAAI,CAAC,sBAAsB,GAAG,WAAW,CAAC,GAAG,EAAE;YAC7C,IAAI,CAAC,sBAAsB,EAAE,CAAC;QAChC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,yBAAyB;IACrC,CAAC;IAED;;OAEG;IACK,iBAAiB,CAAC,MAAc;QACtC,MAAM,MAAM,GAAG;YACb,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;YACrD,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;SACtD,CAAC;QAEF,IAAI,IAAI,GAAG,CAAC,CAAC;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACvC,IAAI,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QACrD,CAAC;QAED,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,SAAc;QAC3B,mEAAmE;QACnE,+CAA+C;QAC/C,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,SAAS,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,IAAI,CAAC,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,WAAW,CAAC,OAAY;QAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YACxE,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAe;QAC7B,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,cAAc;YACpB,OAAO;SACR,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,cAAc;SACrB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,cAAc;QACZ,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,aAAa;SACpB,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAwC;QACvD,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,eAAe;YACrB,MAAM;SACP,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,SAA6F;QAC/G,IAAI,CAAC,WAAW,CAAC;YACf,IAAI,EAAE,kBAAkB;YACxB,SAAS;SACV,CAAC,CAAC;IACL,CAAC;CACF"}