{"version": 3, "file": "AuthUI.js", "sourceRoot": "", "sources": ["../../ui/AuthUI.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,aAAa,EAAa,MAAM,6BAA6B,CAAC;AAEvE,MAAM,OAAO,MAAM;IACT,SAAS,CAAc;IACvB,WAAW,GAA+C,QAAQ,CAAC;IACnE,OAAO,GAAqD,IAAI,CAAC;IAEzE,YAAY,SAAsB;QAChC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,EAAE,CAAC;IACpB,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,gCAAgC;QAChC,aAAa,CAAC,aAAa,CAAC,CAAC,KAAgB,EAAE,EAAE;YAC/C,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACK,qBAAqB,CAAC,KAAgB;QAC5C,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YACf,sCAAsC;YACtC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QACxC,CAAC;aAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC1B,gBAAgB;YAChB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;aAAM,CAAC;YACN,kBAAkB;YAClB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;YACvC,IAAI,CAAC,MAAM,EAAE,CAAC;QAChB,CAAC;QAED,mCAAmC;QACnC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACzC,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACK,MAAM;QACZ,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC1C,IAAI,CAAC,oBAAoB,EAAE,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,OAAO;QACb,MAAM,UAAU,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA4JlB,CAAC;QAEF,QAAQ,IAAI,CAAC,WAAW,EAAE,CAAC;YACzB,KAAK,QAAQ;gBACX,OAAO,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3C,KAAK,QAAQ;gBACX,OAAO,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAC3C,KAAK,YAAY;gBACf,OAAO,UAAU,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAC9C,KAAK,KAAK;gBACR,OAAO,UAAU,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC;YACxC;gBACE,OAAO,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAC7C,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAoDN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAkCN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;KAwBN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,OAAO;;;;;;;;;;;;;;;;;;;;KAoBN,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,oBAAoB;QAC1B,eAAe;QACf,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,eAAe;QACf,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACtE,CAAC;QAED,kBAAkB;QAClB,MAAM,aAAa,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QACjE,IAAI,aAAa,EAAE,CAAC;YAClB,aAAa,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAC5E,CAAC;QAED,WAAW;QACX,MAAM,OAAO,GAAG,QAAQ,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACpD,IAAI,OAAO,EAAE,CAAC;YACZ,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,CAAC;QAED,gBAAgB;QAChB,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC3E,CAAC;QAED,oBAAoB;QACpB,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC;QAC3D,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,EAAE;gBAC1C,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC;gBAChC,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,iBAAiB;QACjB,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBAC3C,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAC5B,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,YAAY,GAAG,QAAQ,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;QAC9D,IAAI,YAAY,EAAE,CAAC;YACjB,YAAY,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBAC3C,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAC5B,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC;QAED,MAAM,UAAU,GAAG,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,UAAU,EAAE,CAAC;YACf,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,EAAE;gBACzC,CAAC,CAAC,cAAc,EAAE,CAAC;gBACnB,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;gBAC5B,IAAI,CAAC,MAAM,EAAE,CAAC;YAChB,CAAC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,CAAQ;QACjC,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,MAAM,IAAI,GAAG,CAAC,CAAC,MAAyB,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAW,CAAC;QAC9C,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAW,CAAC;QAEpD,MAAM,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,YAAY,CAAC,CAAQ;QACjC,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,MAAM,IAAI,GAAG,CAAC,CAAC,MAAyB,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,MAAM,CAAW,CAAC;QAC5C,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAW,CAAC;QAC9C,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAW,CAAC;QAEpD,MAAM,aAAa,CAAC,MAAM,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe,CAAC,CAAQ;QACpC,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,MAAM,IAAI,GAAG,CAAC,CAAC,MAAyB,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAW,CAAC;QAE9C,MAAM,aAAa,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;IACjD,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,SAAS,CAAC,CAAQ;QAC9B,CAAC,CAAC,cAAc,EAAE,CAAC;QACnB,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,MAAM,IAAI,GAAG,CAAC,CAAC,MAAyB,CAAC;QACzC,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,IAAI,CAAC,CAAC;QACpC,MAAM,IAAI,GAAG,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAW,CAAC;QAEhD,MAAM,aAAa,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IACvF,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,WAAW,CAAC,QAA6B;QACrD,MAAM,aAAa,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;IAChD,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,SAAkB;QAC3C,MAAM,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;QAC5D,IAAI,SAAS,EAAE,CAAC;YACd,IAAI,SAAS,EAAE,CAAC;gBACd,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,SAAS,CAAC,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,KAAoB;QAC3C,MAAM,cAAc,GAAG,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;QAClE,IAAI,cAAc,EAAE,CAAC;YACnB,IAAI,KAAK,EAAE,CAAC;gBACV,cAAc,CAAC,SAAS,GAAG,8BAA8B,KAAK,QAAQ,CAAC;YACzE,CAAC;iBAAM,CAAC;gBACN,cAAc,CAAC,SAAS,GAAG,EAAE,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,IAAI;QACF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;QACvC,IAAI,CAAC,MAAM,EAAE,CAAC;IAChB,CAAC;IAED;;OAEG;IACH,IAAI;QACF,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACxC,CAAC;CACF"}