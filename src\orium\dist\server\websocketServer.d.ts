/**
 * WebSocket server for real-time collaboration using Yjs
 * This server handles document synchronization and user presence
 */
export interface CollaborationServer {
    start(port: number): Promise<void>;
    stop(): Promise<void>;
    getConnectedUsers(roomName: string): number;
    getRooms(): string[];
}
export declare class OriumWebSocketServer implements CollaborationServer {
    private wss;
    private server;
    private port;
    private rooms;
    private userSessions;
    /**
     * Start the WebSocket server
     */
    start(port?: number): Promise<void>;
    /**
     * Stop the WebSocket server
     */
    stop(): Promise<void>;
    /**
     * Handle new WebSocket connection
     */
    private handleConnection;
    /**
     * Handle WebSocket disconnection
     */
    private handleDisconnection;
    /**
     * Send message to a specific client
     */
    private sendToClient;
    /**
     * Broadcast message to all users in a room
     */
    private broadcastToRoom;
    /**
     * Get number of connected users in a room
     */
    getConnectedUsers(roomName: string): number;
    /**
     * Get list of all active rooms
     */
    getRooms(): string[];
    /**
     * Get user info for a room
     */
    getRoomUsers(roomName: string): Array<{
        userId: string;
        userName: string;
    }>;
    /**
     * Send message to specific user
     */
    sendToUser(roomName: string, userId: string, message: any): boolean;
    /**
     * Broadcast presence update
     */
    broadcastPresence(roomName: string, userId: string, presence: any): void;
    /**
     * Handle custom messages (for chat, presence, etc.)
     */
    private handleCustomMessage;
    /**
     * Get server statistics
     */
    getStats(): {
        totalRooms: number;
        totalUsers: number;
        rooms: Array<{
            name: string;
            users: number;
        }>;
    };
}
export declare const collaborationServer: OriumWebSocketServer;
//# sourceMappingURL=websocketServer.d.ts.map