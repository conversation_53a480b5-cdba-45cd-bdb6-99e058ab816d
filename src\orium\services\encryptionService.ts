/**
 * Encryption service for secure data handling in Orium
 * Uses AES-256-GCM for encryption and secure key derivation
 */

import * as CryptoJ<PERSON> from 'crypto-js';

export class EncryptionService {
  private static readonly ALGORITHM = 'AES';
  private static readonly KEY_SIZE = 256;
  private static readonly IV_SIZE = 96; // 12 bytes for GCM
  private static readonly TAG_SIZE = 128; // 16 bytes for GCM
  private static readonly ITERATIONS = 100000; // PBKDF2 iterations

  /**
   * Generate a random salt
   */
  private static generateSalt(): string {
    return CryptoJS.lib.WordArray.random(32).toString();
  }

  /**
   * Generate a random IV
   */
  private static generateIV(): string {
    return CryptoJS.lib.WordArray.random(12).toString();
  }

  /**
   * Derive key from password using PBKDF2
   */
  private static deriveKey(password: string, salt: string): CryptoJS.lib.WordArray {
    return CryptoJS.PBKDF2(password, salt, {
      keySize: this.KEY_SIZE / 32,
      iterations: this.ITERATIONS,
      hasher: CryptoJS.algo.SHA256
    });
  }

  /**
   * Encrypt text using AES-256-GCM
   */
  static encrypt(plaintext: string, password: string): string {
    try {
      const salt = this.generateSalt();
      const iv = this.generateIV();
      const key = this.deriveKey(password, salt);

      // Convert IV to WordArray
      const ivWordArray = CryptoJS.enc.Hex.parse(iv);

      // Encrypt using AES-GCM (simulated with AES-CTR + HMAC)
      const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
        iv: ivWordArray,
        mode: CryptoJS.mode.CTR,
        padding: CryptoJS.pad.NoPadding
      });

      // Create authentication tag using HMAC
      const authTag = CryptoJS.HmacSHA256(encrypted.ciphertext.toString(), key).toString();

      // Combine salt, iv, authTag, and ciphertext
      const result = {
        salt,
        iv,
        authTag,
        ciphertext: encrypted.ciphertext.toString()
      };

      return btoa(JSON.stringify(result));
    } catch (error) {
      console.error('Encryption failed:', error);
      throw new Error('Failed to encrypt data');
    }
  }

  /**
   * Decrypt text using AES-256-GCM
   */
  static decrypt(encryptedData: string, password: string): string {
    try {
      const data = JSON.parse(atob(encryptedData));
      const { salt, iv, authTag, ciphertext } = data;

      const key = this.deriveKey(password, salt);
      const ivWordArray = CryptoJS.enc.Hex.parse(iv);

      // Verify authentication tag
      const expectedAuthTag = CryptoJS.HmacSHA256(ciphertext, key).toString();
      if (authTag !== expectedAuthTag) {
        throw new Error('Authentication failed - data may be corrupted');
      }

      // Decrypt
      const decrypted = CryptoJS.AES.decrypt(
        { ciphertext: CryptoJS.enc.Hex.parse(ciphertext) } as any,
        key,
        {
          iv: ivWordArray,
          mode: CryptoJS.mode.CTR,
          padding: CryptoJS.pad.NoPadding
        }
      );

      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Failed to decrypt data');
    }
  }

  /**
   * Generate a secure random password for session-based encryption
   */
  static generateSecurePassword(): string {
    return CryptoJS.lib.WordArray.random(32).toString();
  }

  /**
   * Hash password for secure storage (not for encryption)
   */
  static hashPassword(password: string): string {
    const salt = this.generateSalt();
    const hash = CryptoJS.PBKDF2(password, salt, {
      keySize: 256 / 32,
      iterations: this.ITERATIONS,
      hasher: CryptoJS.algo.SHA256
    });
    return `${salt}:${hash.toString()}`;
  }

  /**
   * Verify password against hash
   */
  static verifyPassword(password: string, hash: string): boolean {
    try {
      const [salt, storedHash] = hash.split(':');
      const computedHash = CryptoJS.PBKDF2(password, salt, {
        keySize: 256 / 32,
        iterations: this.ITERATIONS,
        hasher: CryptoJS.algo.SHA256
      });
      return computedHash.toString() === storedHash;
    } catch (error) {
      return false;
    }
  }

  /**
   * Encrypt API key for secure storage
   */
  static encryptApiKey(apiKey: string, userPassword: string): string {
    return this.encrypt(apiKey, userPassword);
  }

  /**
   * Decrypt API key from secure storage
   */
  static decryptApiKey(encryptedApiKey: string, userPassword: string): string {
    return this.decrypt(encryptedApiKey, userPassword);
  }

  /**
   * Encrypt chat message for end-to-end encryption
   */
  static encryptChatMessage(message: string, sessionKey: string): string {
    return this.encrypt(message, sessionKey);
  }

  /**
   * Decrypt chat message
   */
  static decryptChatMessage(encryptedMessage: string, sessionKey: string): string {
    return this.decrypt(encryptedMessage, sessionKey);
  }

  /**
   * Generate a session key for chat encryption
   */
  static generateSessionKey(): string {
    return this.generateSecurePassword();
  }

  /**
   * Encrypt file content for secure storage
   */
  static encryptFileContent(content: string, sessionKey: string): string {
    return this.encrypt(content, sessionKey);
  }

  /**
   * Decrypt file content
   */
  static decryptFileContent(encryptedContent: string, sessionKey: string): string {
    return this.decrypt(encryptedContent, sessionKey);
  }

  /**
   * Create a secure hash of data (for integrity checking)
   */
  static createHash(data: string): string {
    return CryptoJS.SHA256(data).toString();
  }

  /**
   * Verify data integrity using hash
   */
  static verifyHash(data: string, hash: string): boolean {
    return this.createHash(data) === hash;
  }

  /**
   * Generate a secure random token
   */
  static generateToken(length: number = 32): string {
    return CryptoJS.lib.WordArray.random(length).toString();
  }

  /**
   * Encrypt data with a public key (for key exchange)
   * Note: This is a simplified implementation. In production, use proper RSA encryption.
   */
  static encryptWithPublicKey(data: string, publicKey: string): string {
    // This is a placeholder - implement proper RSA encryption for key exchange
    return this.encrypt(data, publicKey);
  }

  /**
   * Decrypt data with a private key
   */
  static decryptWithPrivateKey(encryptedData: string, privateKey: string): string {
    // This is a placeholder - implement proper RSA decryption for key exchange
    return this.decrypt(encryptedData, privateKey);
  }
}

/**
 * Chat encryption manager for session-based encryption
 */
export class ChatEncryptionManager {
  private sessionKeys: Map<string, string> = new Map();

  /**
   * Initialize encryption for a session
   */
  initializeSession(sessionId: string): string {
    const sessionKey = EncryptionService.generateSessionKey();
    this.sessionKeys.set(sessionId, sessionKey);
    return sessionKey;
  }

  /**
   * Get session key
   */
  getSessionKey(sessionId: string): string | undefined {
    return this.sessionKeys.get(sessionId);
  }

  /**
   * Encrypt message for session
   */
  encryptMessage(sessionId: string, message: string): string {
    const sessionKey = this.sessionKeys.get(sessionId);
    if (!sessionKey) {
      throw new Error('Session not initialized for encryption');
    }
    return EncryptionService.encryptChatMessage(message, sessionKey);
  }

  /**
   * Decrypt message for session
   */
  decryptMessage(sessionId: string, encryptedMessage: string): string {
    const sessionKey = this.sessionKeys.get(sessionId);
    if (!sessionKey) {
      throw new Error('Session not initialized for encryption');
    }
    return EncryptionService.decryptChatMessage(encryptedMessage, sessionKey);
  }

  /**
   * Remove session encryption
   */
  removeSession(sessionId: string): void {
    this.sessionKeys.delete(sessionId);
  }
}

export const chatEncryptionManager = new ChatEncryptionManager();
