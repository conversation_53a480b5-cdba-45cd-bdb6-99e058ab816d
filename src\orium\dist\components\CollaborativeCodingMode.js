/**
 * Collaborative Coding Mode Component
 * Manages the collaborative editing interface with live cursors, presence, and real-time sync
 */
import * as monaco from 'monaco-editor';
import { CollaborationService } from '../services/collaborationService';
import { chatService } from '../services/chatService';
import { jitsiService, JitsiService } from '../services/jitsiService';
import { supabaseService } from '../services/supabaseClient';
export class CollaborativeCodingMode {
    config;
    editor = null;
    collaboration;
    session = null;
    document = null;
    isInitialized = false;
    // UI Elements
    container;
    editorContainer = null;
    sidebarContainer = null;
    videoContainer = null;
    chatContainer = null;
    presenceContainer = null;
    // State
    connectedUsers = [];
    chatMessages = [];
    typingUsers = [];
    // Event callbacks
    onUserJoinedCallback;
    onUserLeftCallback;
    onDocumentChangedCallback;
    constructor(container, config) {
        this.container = container;
        this.config = config;
        this.collaboration = new CollaborationService();
        this.setupEventListeners();
    }
    /**
     * Initialize the collaborative coding mode
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        try {
            // Load session data
            await this.loadSession();
            // Create UI
            this.createUI();
            // Initialize Monaco editor
            await this.initializeEditor();
            // Initialize collaboration
            await this.initializeCollaboration();
            // Initialize chat if enabled
            if (this.config.enableChat) {
                await this.initializeChat();
            }
            // Initialize video if enabled
            if (this.config.enableVideo) {
                await this.initializeVideo();
            }
            this.isInitialized = true;
            console.log('Collaborative coding mode initialized');
        }
        catch (error) {
            console.error('Failed to initialize collaborative mode:', error);
            throw error;
        }
    }
    /**
     * Load session data from Supabase
     */
    async loadSession() {
        const { data: session, error } = await supabaseService.getSession(this.config.sessionId);
        if (error || !session) {
            throw new Error('Failed to load session');
        }
        this.session = session;
        // Load session documents
        const { data: documents } = await supabaseService.getSessionDocuments(this.config.sessionId);
        if (documents && documents.length > 0) {
            this.document = documents[0]; // Use first document for now
        }
    }
    /**
     * Create the UI layout
     */
    createUI() {
        this.container.innerHTML = `
      <div class="collaborative-mode">
        <div class="main-content">
          <div class="editor-section">
            <div class="editor-header">
              <div class="session-info">
                <h2>${this.session?.name || 'Collaborative Session'}</h2>
                <div class="presence-indicators" id="presence-container"></div>
              </div>
              <div class="editor-controls">
                <button id="save-btn" class="btn btn-primary">Save</button>
                <button id="share-btn" class="btn btn-secondary">Share</button>
              </div>
            </div>
            <div class="editor-container" id="editor-container"></div>
          </div>
          ${this.config.enableVideo ? '<div class="video-section" id="video-container"></div>' : ''}
        </div>
        ${this.config.enableChat ? '<div class="sidebar" id="sidebar-container"></div>' : ''}
      </div>
    `;
        // Get references to containers
        this.editorContainer = document.getElementById('editor-container');
        this.presenceContainer = document.getElementById('presence-container');
        this.videoContainer = document.getElementById('video-container');
        this.sidebarContainer = document.getElementById('sidebar-container');
        // Apply styles
        this.applyStyles();
    }
    /**
     * Apply CSS styles
     */
    applyStyles() {
        const style = document.createElement('style');
        style.textContent = `
      .collaborative-mode {
        display: flex;
        height: 100vh;
        background: #1e1e1e;
        color: #ffffff;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }
      
      .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      
      .editor-section {
        flex: 1;
        display: flex;
        flex-direction: column;
      }
      
      .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem;
        background: #2d2d2d;
        border-bottom: 1px solid #404040;
      }
      
      .session-info h2 {
        margin: 0 0 0.5rem 0;
        font-size: 1.2rem;
        color: #ffffff;
      }
      
      .presence-indicators {
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }
      
      .user-indicator {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        padding: 0.25rem 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        font-size: 0.8rem;
      }
      
      .user-color {
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
      
      .editor-container {
        flex: 1;
        position: relative;
      }
      
      .video-section {
        height: 200px;
        background: #000000;
        border-top: 1px solid #404040;
        position: relative;
      }
      
      .sidebar {
        width: 300px;
        background: #252526;
        border-left: 1px solid #404040;
        display: flex;
        flex-direction: column;
      }
      
      .chat-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 1rem;
      }
      
      .chat-messages {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 1rem;
        padding: 0.5rem;
        background: #1e1e1e;
        border-radius: 4px;
        border: 1px solid #404040;
      }
      
      .chat-message {
        margin-bottom: 0.5rem;
        padding: 0.5rem;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.05);
      }
      
      .chat-input {
        display: flex;
        gap: 0.5rem;
      }
      
      .chat-input input {
        flex: 1;
        padding: 0.5rem;
        border: 1px solid #404040;
        border-radius: 4px;
        background: #1e1e1e;
        color: #ffffff;
      }
      
      .btn {
        padding: 0.5rem 1rem;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.9rem;
        transition: background-color 0.2s;
      }
      
      .btn-primary {
        background: #007ACC;
        color: #ffffff;
      }
      
      .btn-primary:hover {
        background: #005a9e;
      }
      
      .btn-secondary {
        background: #404040;
        color: #ffffff;
      }
      
      .btn-secondary:hover {
        background: #505050;
      }
      
      .typing-indicator {
        font-style: italic;
        color: #888888;
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
      }
    `;
        document.head.appendChild(style);
    }
    /**
     * Initialize Monaco editor
     */
    async initializeEditor() {
        if (!this.editorContainer) {
            throw new Error('Editor container not found');
        }
        // Configure Monaco editor
        this.editor = monaco.editor.create(this.editorContainer, {
            value: this.document?.content || '// Welcome to Orium Collaborative Coding\n// Start typing to collaborate in real-time!\n',
            language: this.document?.language || 'javascript',
            theme: 'vs-dark',
            automaticLayout: true,
            fontSize: 14,
            lineNumbers: 'on',
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            wordWrap: 'on',
            // Collaboration-specific settings
            cursorBlinking: 'smooth',
            cursorSmoothCaretAnimation: 'on',
            renderWhitespace: 'selection'
        });
        // Listen for content changes
        this.editor.onDidChangeModelContent(() => {
            if (this.onDocumentChangedCallback) {
                this.onDocumentChangedCallback(this.editor.getValue());
            }
        });
        console.log('Monaco editor initialized');
    }
    /**
     * Initialize collaboration
     */
    async initializeCollaboration() {
        if (!this.editor) {
            throw new Error('Editor not initialized');
        }
        // Initialize collaboration service
        await this.collaboration.initializeSession(this.config.sessionId, this.config.websocketUrl);
        // Bind editor to collaboration
        this.collaboration.bindEditor(this.editor);
        // Set up event listeners
        this.collaboration.onUsersChanged((users) => {
            this.connectedUsers = users;
            this.updatePresenceIndicators();
            // Notify about user changes
            users.forEach(user => {
                if (this.onUserJoinedCallback) {
                    this.onUserJoinedCallback(user);
                }
            });
        });
        this.collaboration.onCursorChanged((userId, cursor) => {
            this.updateUserCursor(userId, cursor);
        });
        this.collaboration.onSelectionChanged((userId, selection) => {
            this.updateUserSelection(userId, selection);
        });
        console.log('Collaboration initialized');
    }
    /**
     * Initialize chat
     */
    async initializeChat() {
        if (!this.sidebarContainer)
            return;
        // Create chat UI
        this.sidebarContainer.innerHTML = `
      <div class="chat-container">
        <h3>Chat</h3>
        <div class="chat-messages" id="chat-messages"></div>
        <div class="typing-indicator" id="typing-indicator"></div>
        <div class="chat-input">
          <input type="text" id="chat-input" placeholder="Type a message..." />
          <button id="send-btn" class="btn btn-primary">Send</button>
        </div>
      </div>
    `;
        this.chatContainer = document.getElementById('chat-messages');
        // Initialize chat service
        await chatService.initializeSession(this.config.sessionId, true);
        // Set up event listeners
        chatService.onMessage((message) => {
            this.addChatMessage(message);
        });
        chatService.onMessagesLoaded((messages) => {
            this.chatMessages = messages;
            this.renderChatMessages();
        });
        chatService.onTypingChanged((typingUsers) => {
            this.typingUsers = typingUsers;
            this.updateTypingIndicator();
        });
        // Set up chat input
        const chatInput = document.getElementById('chat-input');
        const sendBtn = document.getElementById('send-btn');
        if (chatInput && sendBtn) {
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendChatMessage();
                }
            });
            sendBtn.addEventListener('click', () => {
                this.sendChatMessage();
            });
        }
        console.log('Chat initialized');
    }
    /**
     * Initialize video conferencing
     */
    async initializeVideo() {
        if (!this.videoContainer)
            return;
        const user = supabaseService.getCurrentUser();
        if (!user)
            return;
        const config = JitsiService.createConfig(this.config.sessionId, {
            displayName: user.user_metadata?.name || user.email || 'Anonymous',
            email: user.email
        });
        await jitsiService.initialize(this.videoContainer, config);
        console.log('Video conferencing initialized');
    }
    /**
     * Update presence indicators
     */
    updatePresenceIndicators() {
        if (!this.presenceContainer)
            return;
        this.presenceContainer.innerHTML = this.connectedUsers.map(user => `
      <div class="user-indicator">
        <div class="user-color" style="background-color: ${user.color}"></div>
        <span>${user.name}</span>
      </div>
    `).join('');
    }
    /**
     * Update user cursor position
     */
    updateUserCursor(userId, cursor) {
        // This would update the visual cursor indicator in the editor
        // Monaco editor with Yjs binding handles this automatically
        console.log(`User ${userId} cursor at line ${cursor.line}, column ${cursor.column}`);
    }
    /**
     * Update user selection
     */
    updateUserSelection(userId, selection) {
        // This would update the visual selection indicator in the editor
        // Monaco editor with Yjs binding handles this automatically
        console.log(`User ${userId} selection updated`);
    }
    /**
     * Send chat message
     */
    async sendChatMessage() {
        const chatInput = document.getElementById('chat-input');
        if (!chatInput || !chatInput.value.trim())
            return;
        try {
            await chatService.sendMessage(chatInput.value);
            chatInput.value = '';
        }
        catch (error) {
            console.error('Failed to send chat message:', error);
        }
    }
    /**
     * Add chat message to UI
     */
    addChatMessage(message) {
        this.chatMessages.push(message);
        this.renderChatMessage(message);
        this.scrollChatToBottom();
    }
    /**
     * Render all chat messages
     */
    renderChatMessages() {
        if (!this.chatContainer)
            return;
        this.chatContainer.innerHTML = this.chatMessages.map(message => this.getChatMessageHTML(message)).join('');
        this.scrollChatToBottom();
    }
    /**
     * Render single chat message
     */
    renderChatMessage(message) {
        if (!this.chatContainer)
            return;
        const messageElement = document.createElement('div');
        messageElement.innerHTML = this.getChatMessageHTML(message);
        this.chatContainer.appendChild(messageElement.firstElementChild);
    }
    /**
     * Get chat message HTML
     */
    getChatMessageHTML(message) {
        const timestamp = new Date(message.created_at).toLocaleTimeString();
        return `
      <div class="chat-message">
        <div class="message-header">
          <strong>${message.user.name}</strong>
          <span class="timestamp">${timestamp}</span>
        </div>
        <div class="message-content">${this.formatMessageContent(message.content)}</div>
      </div>
    `;
    }
    /**
     * Format message content (handle code blocks, etc.)
     */
    formatMessageContent(content) {
        // Simple formatting - in production, use a proper markdown parser
        return content
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            .replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>');
    }
    /**
     * Update typing indicator
     */
    updateTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (!indicator)
            return;
        if (this.typingUsers.length === 0) {
            indicator.textContent = '';
        }
        else if (this.typingUsers.length === 1) {
            indicator.textContent = `${this.typingUsers[0].name} is typing...`;
        }
        else {
            indicator.textContent = `${this.typingUsers.length} people are typing...`;
        }
    }
    /**
     * Scroll chat to bottom
     */
    scrollChatToBottom() {
        if (this.chatContainer) {
            this.chatContainer.scrollTop = this.chatContainer.scrollHeight;
        }
    }
    /**
     * Set up event listeners
     */
    setupEventListeners() {
        // Document save
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                this.saveDocument();
            }
        });
    }
    /**
     * Save document
     */
    async saveDocument() {
        if (!this.editor || !this.session)
            return;
        try {
            const content = this.editor.getValue();
            if (this.document) {
                // Update existing document
                // This would be implemented with Supabase update
                console.log('Document saved');
            }
            else {
                // Create new document
                const { data, error } = await supabaseService.createDocument(this.session.id, 'main.js', // Default name
                content, 'javascript');
                if (!error && data) {
                    this.document = data;
                    console.log('Document created and saved');
                }
            }
        }
        catch (error) {
            console.error('Failed to save document:', error);
        }
    }
    /**
     * Get current document content
     */
    getDocumentContent() {
        return this.editor?.getValue() || '';
    }
    /**
     * Set document content
     */
    setDocumentContent(content) {
        if (this.editor) {
            this.editor.setValue(content);
        }
    }
    /**
     * Get connected users
     */
    getConnectedUsers() {
        return [...this.connectedUsers];
    }
    /**
     * Dispose of the collaborative mode
     */
    dispose() {
        // Disconnect services
        this.collaboration.disconnect();
        chatService.disconnect();
        jitsiService.dispose();
        // Dispose editor
        if (this.editor) {
            this.editor.dispose();
        }
        // Clear container
        this.container.innerHTML = '';
        this.isInitialized = false;
        console.log('Collaborative coding mode disposed');
    }
    /**
     * Set event callbacks
     */
    onUserJoined(callback) {
        this.onUserJoinedCallback = callback;
    }
    onUserLeft(callback) {
        this.onUserLeftCallback = callback;
    }
    onDocumentChanged(callback) {
        this.onDocumentChangedCallback = callback;
    }
}
//# sourceMappingURL=CollaborativeCodingMode.js.map