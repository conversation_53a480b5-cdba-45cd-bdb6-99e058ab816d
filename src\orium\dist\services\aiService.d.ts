/**
 * AI service for Vibe Coding Mode
 * Supports multiple AI providers: OpenAI, Anthropic, Google, Cohere
 */
export interface AIProvider {
    name: string;
    id: string;
    models: string[];
    requiresApiKey: boolean;
}
export interface AIRequest {
    prompt: string;
    context?: string;
    language?: string;
    maxTokens?: number;
    temperature?: number;
}
export interface AIResponse {
    content: string;
    model: string;
    provider: string;
    usage?: {
        promptTokens: number;
        completionTokens: number;
        totalTokens: number;
    };
}
export interface CodeSuggestion {
    code: string;
    description: string;
    confidence: number;
    startLine: number;
    endLine: number;
}
export declare class AIService {
    private providers;
    private apiKeys;
    private currentProvider;
    private currentModel;
    private userPassword;
    constructor();
    /**
     * Initialize AI providers
     */
    private initializeProviders;
    /**
     * Set user password for API key encryption/decryption
     */
    setUserPassword(password: string): void;
    /**
     * Load API keys from Supabase
     */
    loadApiKeys(): Promise<void>;
    /**
     * Save API key securely
     */
    saveApiKey(provider: string, apiKey: string): Promise<void>;
    /**
     * Get available providers
     */
    getProviders(): AIProvider[];
    /**
     * Set current provider and model
     */
    setProvider(providerId: string, model?: string): void;
    /**
     * Get current provider and model
     */
    getCurrentProvider(): {
        provider: AIProvider;
        model: string;
    };
    /**
     * Check if API key is available for current provider
     */
    hasApiKey(providerId?: string): boolean;
    /**
     * Generate code completion
     */
    generateCompletion(request: AIRequest): Promise<AIResponse>;
    /**
     * Generate code suggestions
     */
    generateCodeSuggestions(code: string, cursorPosition: {
        line: number;
        column: number;
    }, language: string): Promise<CodeSuggestion[]>;
    /**
     * Explain code
     */
    explainCode(code: string, language: string): Promise<string>;
    /**
     * Fix code errors
     */
    fixCode(code: string, error: string, language: string): Promise<string>;
    /**
     * Generate unit tests
     */
    generateTests(code: string, language: string): Promise<string>;
    /**
     * Call OpenAI API
     */
    private callOpenAI;
    /**
     * Call Anthropic API
     */
    private callAnthropic;
    /**
     * Call Google API (placeholder)
     */
    private callGoogle;
    /**
     * Call Cohere API (placeholder)
     */
    private callCohere;
    /**
     * Build code suggestion prompt
     */
    private buildCodeSuggestionPrompt;
    /**
     * Parse code suggestions from AI response
     */
    private parseCodeSuggestions;
}
export declare const aiService: AIService;
//# sourceMappingURL=aiService.d.ts.map