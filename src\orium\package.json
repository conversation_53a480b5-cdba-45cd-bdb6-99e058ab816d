{"name": "orium", "version": "1.0.0", "description": "Secure AI-powered real-time collaborative code editor", "main": "OriumApp.js", "type": "module", "scripts": {"dev": "npm run build && npm run serve", "build": "tsc && npm run copy-assets", "build:watch": "tsc --watch", "copy-assets": "cp index.html dist/ && cp -r assets dist/ 2>/dev/null || true", "serve": "npx http-server dist -p 3000 -c-1 --cors", "start": "npm run build && npm run serve", "start:server": "node server/websocketServer.js", "start:dev": "concurrently \"npm run build:watch\" \"npm run serve\" \"npm run start:server\"", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "test": "jest", "test:watch": "jest --watch", "clean": "rm -rf dist", "typecheck": "tsc --noEmit"}, "keywords": ["collaborative", "code-editor", "real-time", "ai-powered", "secure", "typescript", "monaco-editor", "yjs", "supabase"], "author": "Orium Team", "license": "MIT", "dependencies": {"@supabase/supabase-js": "^2.39.0", "crypto-js": "^4.2.0", "lib0": "^0.2.85", "monaco-editor": "^0.45.0", "socket.io-client": "^4.7.4", "ws": "^8.16.0", "y-monaco": "^0.1.4", "y-websocket": "^1.5.0", "yjs": "^13.6.10"}, "devDependencies": {"@types/crypto-js": "^4.2.1", "@types/jest": "^29.5.8", "@types/node": "^20.10.0", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "concurrently": "^8.2.2", "eslint": "^8.54.0", "http-server": "^14.1.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/orium/orium.git"}, "bugs": {"url": "https://github.com/orium/orium/issues"}, "homepage": "https://orium.dev"}