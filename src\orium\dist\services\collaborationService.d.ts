/**
 * Collaboration service using Yjs for real-time collaborative editing
 */
import * as Y from 'yjs';
import type { editor } from 'monaco-editor';
export interface CollaborationUser {
    id: string;
    name: string;
    color: string;
    cursor?: {
        line: number;
        column: number;
    };
    selection?: {
        start: {
            line: number;
            column: number;
        };
        end: {
            line: number;
            column: number;
        };
    };
}
export declare class CollaborationService {
    private ydoc;
    private provider;
    private binding;
    private sessionId;
    private currentUser;
    private users;
    private presenceUpdateInterval;
    private onUsersChangedCallback?;
    private onCursorChangedCallback?;
    private onSelectionChangedCallback?;
    constructor();
    /**
     * Initialize collaboration for a session
     */
    initializeSession(sessionId: string, websocketUrl?: string): Promise<void>;
    /**
     * Bind Monaco editor to Yjs document
     */
    bindEditor(monacoEditor: editor.IStandaloneCodeEditor): void;
    /**
     * Disconnect from collaboration
     */
    disconnect(): void;
    /**
     * Get current document content
     */
    getDocumentContent(): string;
    /**
     * Set document content (use carefully - this will overwrite)
     */
    setDocumentContent(content: string): void;
    /**
     * Get list of connected users
     */
    getConnectedUsers(): CollaborationUser[];
    /**
     * Set callback for when users change
     */
    onUsersChanged(callback: (users: CollaborationUser[]) => void): void;
    /**
     * Set callback for cursor changes
     */
    onCursorChanged(callback: (userId: string, cursor: {
        line: number;
        column: number;
    }) => void): void;
    /**
     * Set callback for selection changes
     */
    onSelectionChanged(callback: (userId: string, selection: {
        start: {
            line: number;
            column: number;
        };
        end: {
            line: number;
            column: number;
        };
    }) => void): void;
    /**
     * Handle awareness changes (user presence)
     */
    private handleAwarenessChange;
    /**
     * Handle WebSocket messages
     */
    private handleWebSocketMessage;
    /**
     * Handle cursor position changes
     */
    private handleCursorChange;
    /**
     * Handle selection changes
     */
    private handleSelectionChange;
    /**
     * Update presence in Supabase
     */
    private updateSupabasePresence;
    /**
     * Start periodic presence updates
     */
    private startPresenceUpdates;
    /**
     * Generate a consistent color for a user based on their ID
     */
    private generateUserColor;
    /**
     * Apply operation to document (for undo/redo)
     */
    applyOperation(operation: any): void;
    /**
     * Get undo manager for collaborative undo/redo
     */
    getUndoManager(): Y.UndoManager;
    /**
     * Send custom message through WebSocket
     */
    private sendMessage;
    /**
     * Send chat message
     */
    sendChatMessage(content: string): void;
    /**
     * Send typing indicator
     */
    sendTypingStart(): void;
    /**
     * Send typing stop indicator
     */
    sendTypingStop(): void;
    /**
     * Send cursor position update
     */
    sendCursorUpdate(cursor: {
        line: number;
        column: number;
    }): void;
    /**
     * Send selection update
     */
    sendSelectionUpdate(selection: {
        start: {
            line: number;
            column: number;
        };
        end: {
            line: number;
            column: number;
        };
    }): void;
}
//# sourceMappingURL=collaborationService.d.ts.map