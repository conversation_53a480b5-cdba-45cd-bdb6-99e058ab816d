/**
 * Jitsi Meet integration service for video conferencing
 */

import type { JitsiConfig } from '../types';

declare global {
  interface Window {
    JitsiMeetExternalAPI: any;
  }
}

export interface JitsiMeetAPI {
  dispose(): void;
  executeCommand(command: string, ...args: any[]): void;
  getVideoQuality(): string;
  isAudioMuted(): Promise<boolean>;
  isVideoMuted(): Promise<boolean>;
  addEventListener(event: string, listener: Function): void;
  removeEventListener(event: string, listener: Function): void;
}

export class JitsiService {
  private api: JitsiMeetAPI | null = null;
  private container: HTMLElement | null = null;
  private isInitialized: boolean = false;
  private currentConfig: JitsiConfig | null = null;

  // Event callbacks
  private onReadyCallback?: () => void;
  private onParticipantJoinedCallback?: (participant: any) => void;
  private onParticipantLeftCallback?: (participant: any) => void;
  private onVideoConferenceJoinedCallback?: (participant: any) => void;
  private onVideoConferenceLeftCallback?: () => void;

  constructor() {
    this.loadJitsiScript();
  }

  /**
   * Load Jitsi Meet External API script
   */
  private async loadJitsiScript(): Promise<void> {
    if (window.JitsiMeetExternalAPI) {
      return;
    }

    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = 'https://meet.jit.si/external_api.js';
      script.async = true;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Jitsi Meet API'));
      document.head.appendChild(script);
    });
  }

  /**
   * Initialize Jitsi Meet conference
   */
  async initialize(container: HTMLElement, config: JitsiConfig): Promise<void> {
    if (this.isInitialized) {
      await this.dispose();
    }

    await this.loadJitsiScript();

    this.container = container;
    this.currentConfig = config;

    const options = {
      roomName: config.roomName,
      width: '100%',
      height: '100%',
      parentNode: container,
      jwt: config.jwt,
      userInfo: config.userInfo,
      configOverwrite: {
        startWithAudioMuted: false,
        startWithVideoMuted: false,
        enableWelcomePage: false,
        enableClosePage: false,
        prejoinPageEnabled: false,
        disableInviteFunctions: true,
        doNotStoreRoom: true,
        // Security settings
        enableE2EE: true,
        e2eeLabels: {
          tooltip: 'End-to-end encryption enabled',
          warning: 'End-to-end encryption is experimental'
        },
        // UI customization
        toolbarButtons: [
          'microphone',
          'camera',
          'closedcaptions',
          'desktop',
          'fullscreen',
          'fodeviceselection',
          'hangup',
          'profile',
          'chat',
          'recording',
          'livestreaming',
          'etherpad',
          'sharedvideo',
          'settings',
          'raisehand',
          'videoquality',
          'filmstrip',
          'invite',
          'feedback',
          'stats',
          'shortcuts',
          'tileview',
          'videobackgroundblur',
          'download',
          'help',
          'mute-everyone',
          'security'
        ]
      },
      interfaceConfigOverwrite: {
        SHOW_JITSI_WATERMARK: false,
        SHOW_WATERMARK_FOR_GUESTS: false,
        SHOW_BRAND_WATERMARK: false,
        BRAND_WATERMARK_LINK: '',
        SHOW_POWERED_BY: false,
        DISPLAY_WELCOME_PAGE_CONTENT: false,
        DISPLAY_WELCOME_PAGE_TOOLBAR_ADDITIONAL_CONTENT: false,
        APP_NAME: 'Orium',
        NATIVE_APP_NAME: 'Orium',
        PROVIDER_NAME: 'Orium',
        // Disable some features for cleaner UI
        TOOLBAR_TIMEOUT: 4000,
        INITIAL_TOOLBAR_TIMEOUT: 20000,
        TOOLBAR_ALWAYS_VISIBLE: false,
        DEFAULT_BACKGROUND: '#1e1e1e',
        // Mobile optimizations
        MOBILE_APP_PROMO: false,
        MOBILE_DOWNLOAD_LINK_ANDROID: '',
        MOBILE_DOWNLOAD_LINK_IOS: ''
      }
    };

    this.api = new window.JitsiMeetExternalAPI(config.domain, options);

    // Set up event listeners
    this.setupEventListeners();

    this.isInitialized = true;
    console.log('Jitsi Meet initialized for room:', config.roomName);
  }

  /**
   * Set up event listeners for Jitsi Meet events
   */
  private setupEventListeners(): void {
    if (!this.api) return;

    this.api.addEventListener('videoConferenceJoined', (participant: any) => {
      console.log('Video conference joined:', participant);
      if (this.onVideoConferenceJoinedCallback) {
        this.onVideoConferenceJoinedCallback(participant);
      }
    });

    this.api.addEventListener('videoConferenceLeft', () => {
      console.log('Video conference left');
      if (this.onVideoConferenceLeftCallback) {
        this.onVideoConferenceLeftCallback();
      }
    });

    this.api.addEventListener('participantJoined', (participant: any) => {
      console.log('Participant joined:', participant);
      if (this.onParticipantJoinedCallback) {
        this.onParticipantJoinedCallback(participant);
      }
    });

    this.api.addEventListener('participantLeft', (participant: any) => {
      console.log('Participant left:', participant);
      if (this.onParticipantLeftCallback) {
        this.onParticipantLeftCallback(participant);
      }
    });

    this.api.addEventListener('readyToClose', () => {
      console.log('Jitsi Meet ready to close');
      this.dispose();
    });

    this.api.addEventListener('audioMuteStatusChanged', (event: any) => {
      console.log('Audio mute status changed:', event);
    });

    this.api.addEventListener('videoMuteStatusChanged', (event: any) => {
      console.log('Video mute status changed:', event);
    });
  }

  /**
   * Dispose of the Jitsi Meet instance
   */
  async dispose(): Promise<void> {
    if (this.api) {
      this.api.dispose();
      this.api = null;
    }

    if (this.container) {
      this.container.innerHTML = '';
    }

    this.isInitialized = false;
    this.currentConfig = null;
    console.log('Jitsi Meet disposed');
  }

  /**
   * Toggle audio mute
   */
  async toggleAudio(): Promise<void> {
    if (!this.api) return;

    const isMuted = await this.api.isAudioMuted();
    this.api.executeCommand('toggleAudio');
    console.log('Audio toggled:', !isMuted);
  }

  /**
   * Toggle video mute
   */
  async toggleVideo(): Promise<void> {
    if (!this.api) return;

    const isMuted = await this.api.isVideoMuted();
    this.api.executeCommand('toggleVideo');
    console.log('Video toggled:', !isMuted);
  }

  /**
   * Start screen sharing
   */
  startScreenShare(): void {
    if (!this.api) return;
    this.api.executeCommand('toggleShareScreen');
  }

  /**
   * End the call
   */
  hangUp(): void {
    if (!this.api) return;
    this.api.executeCommand('hangup');
  }

  /**
   * Set display name
   */
  setDisplayName(name: string): void {
    if (!this.api) return;
    this.api.executeCommand('displayName', name);
  }

  /**
   * Send chat message
   */
  sendChatMessage(message: string): void {
    if (!this.api) return;
    this.api.executeCommand('sendChatMessage', message);
  }

  /**
   * Check if audio is muted
   */
  async isAudioMuted(): Promise<boolean> {
    if (!this.api) return false;
    return await this.api.isAudioMuted();
  }

  /**
   * Check if video is muted
   */
  async isVideoMuted(): Promise<boolean> {
    if (!this.api) return false;
    return await this.api.isVideoMuted();
  }

  /**
   * Get current video quality
   */
  getVideoQuality(): string {
    if (!this.api) return 'unknown';
    return this.api.getVideoQuality();
  }

  /**
   * Set event callbacks
   */
  onReady(callback: () => void): void {
    this.onReadyCallback = callback;
  }

  onParticipantJoined(callback: (participant: any) => void): void {
    this.onParticipantJoinedCallback = callback;
  }

  onParticipantLeft(callback: (participant: any) => void): void {
    this.onParticipantLeftCallback = callback;
  }

  onVideoConferenceJoined(callback: (participant: any) => void): void {
    this.onVideoConferenceJoinedCallback = callback;
  }

  onVideoConferenceLeft(callback: () => void): void {
    this.onVideoConferenceLeftCallback = callback;
  }

  /**
   * Generate secure room name
   */
  static generateRoomName(sessionId: string): string {
    // Create a secure room name based on session ID
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2);
    return `orium-${sessionId}-${timestamp}-${random}`;
  }

  /**
   * Create Jitsi configuration for a session
   */
  static createConfig(sessionId: string, userInfo: { displayName: string; email?: string }): JitsiConfig {
    return {
      domain: 'meet.jit.si',
      roomName: this.generateRoomName(sessionId),
      userInfo
    };
  }

  /**
   * Check if Jitsi is supported in current browser
   */
  static isSupported(): boolean {
    // Check for WebRTC support
    return !!(
      navigator.mediaDevices &&
      typeof navigator.mediaDevices.getUserMedia === 'function' &&
      window.RTCPeerConnection
    );
  }
}

export const jitsiService = new JitsiService();
