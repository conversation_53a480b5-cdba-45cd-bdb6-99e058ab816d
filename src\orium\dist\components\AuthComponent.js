/**
 * Authentication component for Orium
 * Handles sign-up, sign-in, OAuth, and MFA
 */
import { supabaseService } from '../services/supabaseClient';
export class AuthComponent {
    state = {
        user: null,
        isLoading: false,
        error: null,
        needsMFA: false,
        mfaChallenge: null
    };
    onStateChangeCallback;
    constructor() {
        this.initializeAuth();
    }
    /**
     * Initialize authentication state
     */
    async initializeAuth() {
        this.setState({ isLoading: true });
        try {
            const user = supabaseService.getCurrentUser();
            if (user) {
                this.setState({
                    user: {
                        id: user.id,
                        email: user.email || '',
                        name: user.user_metadata?.name,
                        avatar_url: user.user_metadata?.avatar_url,
                        created_at: user.created_at,
                        updated_at: user.updated_at || user.created_at
                    },
                    isLoading: false
                });
            }
            else {
                this.setState({ isLoading: false });
            }
        }
        catch (error) {
            this.setState({
                error: 'Failed to initialize authentication',
                isLoading: false
            });
        }
    }
    /**
     * Sign up with email and password
     */
    async signUp(email, password, name) {
        this.setState({ isLoading: true, error: null });
        try {
            const { data, error } = await supabaseService.signUp(email, password, { name });
            if (error) {
                this.setState({ error: error.message, isLoading: false });
                return;
            }
            if (data.user && !data.session) {
                // Email confirmation required
                this.setState({
                    error: 'Please check your email and click the confirmation link',
                    isLoading: false
                });
            }
            else if (data.user && data.session) {
                // User signed up and logged in
                this.setState({
                    user: {
                        id: data.user.id,
                        email: data.user.email || '',
                        name: data.user.user_metadata?.name,
                        avatar_url: data.user.user_metadata?.avatar_url,
                        created_at: data.user.created_at,
                        updated_at: data.user.updated_at || data.user.created_at
                    },
                    isLoading: false
                });
            }
        }
        catch (error) {
            this.setState({
                error: 'Sign up failed. Please try again.',
                isLoading: false
            });
        }
    }
    /**
     * Sign in with email and password
     */
    async signIn(email, password) {
        this.setState({ isLoading: true, error: null });
        try {
            const { data, error } = await supabaseService.signIn(email, password);
            if (error) {
                this.setState({ error: error.message, isLoading: false });
                return;
            }
            if (data.user) {
                this.setState({
                    user: {
                        id: data.user.id,
                        email: data.user.email || '',
                        name: data.user.user_metadata?.name,
                        avatar_url: data.user.user_metadata?.avatar_url,
                        created_at: data.user.created_at,
                        updated_at: data.user.updated_at || data.user.created_at
                    },
                    isLoading: false
                });
            }
        }
        catch (error) {
            this.setState({
                error: 'Sign in failed. Please try again.',
                isLoading: false
            });
        }
    }
    /**
     * Sign in with OAuth provider
     */
    async signInWithOAuth(provider) {
        this.setState({ isLoading: true, error: null });
        try {
            const { data, error } = await supabaseService.signInWithOAuth(provider);
            if (error) {
                this.setState({ error: error.message, isLoading: false });
                return;
            }
            // OAuth redirect will handle the rest
            console.log('OAuth redirect initiated');
        }
        catch (error) {
            this.setState({
                error: `${provider} sign in failed. Please try again.`,
                isLoading: false
            });
        }
    }
    /**
     * Sign in with passwordless link
     */
    async signInWithMagicLink(email) {
        this.setState({ isLoading: true, error: null });
        try {
            const { data, error } = await supabaseService.signInWithOtp(email);
            if (error) {
                this.setState({ error: error.message, isLoading: false });
                return;
            }
            this.setState({
                error: 'Check your email for the magic link',
                isLoading: false
            });
        }
        catch (error) {
            this.setState({
                error: 'Failed to send magic link. Please try again.',
                isLoading: false
            });
        }
    }
    /**
     * Sign out
     */
    async signOut() {
        this.setState({ isLoading: true, error: null });
        try {
            const { error } = await supabaseService.signOut();
            if (error) {
                this.setState({ error: error.message, isLoading: false });
                return;
            }
            this.setState({
                user: null,
                isLoading: false,
                needsMFA: false,
                mfaChallenge: null
            });
        }
        catch (error) {
            this.setState({
                error: 'Sign out failed. Please try again.',
                isLoading: false
            });
        }
    }
    /**
     * Enable MFA
     */
    async enableMFA() {
        this.setState({ isLoading: true, error: null });
        try {
            const { data, error } = await supabaseService.enableMFA();
            if (error) {
                this.setState({ error: error.message, isLoading: false });
                throw error;
            }
            this.setState({ isLoading: false });
            return {
                qrCode: data?.totp?.qr_code,
                secret: data?.totp?.secret
            };
        }
        catch (error) {
            this.setState({
                error: 'Failed to enable MFA. Please try again.',
                isLoading: false
            });
            throw error;
        }
    }
    /**
     * Verify MFA code
     */
    async verifyMFA(factorId, challengeId, code) {
        this.setState({ isLoading: true, error: null });
        try {
            const { data, error } = await supabaseService.verifyMFA(factorId, challengeId, code);
            if (error) {
                this.setState({ error: error.message, isLoading: false });
                return;
            }
            if (data && data.user) {
                this.setState({
                    user: {
                        id: data.user.id,
                        email: data.user.email || '',
                        name: data.user.user_metadata?.name,
                        avatar_url: data.user.user_metadata?.avatar_url,
                        created_at: data.user.created_at,
                        updated_at: data.user.updated_at || data.user.created_at
                    },
                    needsMFA: false,
                    mfaChallenge: null,
                    isLoading: false
                });
            }
        }
        catch (error) {
            this.setState({
                error: 'MFA verification failed. Please try again.',
                isLoading: false
            });
        }
    }
    /**
     * Get current authentication state
     */
    getState() {
        return { ...this.state };
    }
    /**
     * Set state change callback
     */
    onStateChange(callback) {
        this.onStateChangeCallback = callback;
    }
    /**
     * Update state and notify listeners
     */
    setState(updates) {
        this.state = { ...this.state, ...updates };
        if (this.onStateChangeCallback) {
            this.onStateChangeCallback(this.getState());
        }
    }
    /**
     * Check if user is authenticated
     */
    isAuthenticated() {
        return this.state.user !== null;
    }
    /**
     * Get current user
     */
    getCurrentUser() {
        return this.state.user;
    }
    /**
     * Handle OAuth callback
     */
    async handleOAuthCallback() {
        // This would be called when the OAuth redirect returns
        const user = supabaseService.getCurrentUser();
        if (user) {
            this.setState({
                user: {
                    id: user.id,
                    email: user.email || '',
                    name: user.user_metadata?.name,
                    avatar_url: user.user_metadata?.avatar_url,
                    created_at: user.created_at,
                    updated_at: user.updated_at || user.created_at
                },
                isLoading: false
            });
        }
    }
}
export const authComponent = new AuthComponent();
//# sourceMappingURL=AuthComponent.js.map