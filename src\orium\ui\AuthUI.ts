/**
 * Authentication UI for Orium
 * Creates and manages authentication forms and modals
 */

import { authComponent, AuthState } from '../components/AuthComponent';

export class AuthUI {
  private container: HTMLElement;
  private currentView: 'signin' | 'signup' | 'magic-link' | 'mfa' = 'signin';
  private mfaData: { factorId: string; challengeId: string } | null = null;

  constructor(container: HTMLElement) {
    this.container = container;
    this.initialize();
  }

  /**
   * Initialize the authentication UI
   */
  private initialize(): void {
    // Listen for auth state changes
    authComponent.onStateChange((state: AuthState) => {
      this.handleAuthStateChange(state);
    });

    // Create initial UI
    this.render();
  }

  /**
   * Handle authentication state changes
   */
  private handleAuthStateChange(state: AuthState): void {
    if (state.user) {
      // User is authenticated, hide auth UI
      this.container.style.display = 'none';
    } else if (state.needsMFA) {
      // Show MFA form
      this.currentView = 'mfa';
      this.render();
    } else {
      // Show auth forms
      this.container.style.display = 'block';
      this.render();
    }

    // Update loading states and errors
    this.updateLoadingState(state.isLoading);
    this.updateErrorState(state.error);
  }

  /**
   * Render the authentication UI
   */
  private render(): void {
    this.container.innerHTML = this.getHTML();
    this.attachEventListeners();
  }

  /**
   * Get HTML for current view
   */
  private getHTML(): string {
    const baseStyles = `
      <style>
        .orium-auth {
          display: flex;
          align-items: center;
          justify-content: center;
          min-height: 100vh;
          background: linear-gradient(135deg, #1e1e1e 0%, #2d2d2d 100%);
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .auth-container {
          background: #2d2d2d;
          border-radius: 12px;
          padding: 2rem;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
          width: 100%;
          max-width: 400px;
          border: 1px solid #404040;
        }
        .auth-header {
          text-align: center;
          margin-bottom: 2rem;
        }
        .auth-title {
          color: #ffffff;
          font-size: 2rem;
          font-weight: 600;
          margin: 0 0 0.5rem 0;
        }
        .auth-subtitle {
          color: #b0b0b0;
          font-size: 0.9rem;
          margin: 0;
        }
        .form-group {
          margin-bottom: 1rem;
        }
        .form-label {
          display: block;
          color: #ffffff;
          font-size: 0.9rem;
          margin-bottom: 0.5rem;
          font-weight: 500;
        }
        .form-input {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #404040;
          border-radius: 6px;
          background: #1e1e1e;
          color: #ffffff;
          font-size: 0.9rem;
          transition: border-color 0.2s;
          box-sizing: border-box;
        }
        .form-input:focus {
          outline: none;
          border-color: #007ACC;
          box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.2);
        }
        .btn {
          width: 100%;
          padding: 0.75rem;
          border: none;
          border-radius: 6px;
          font-size: 0.9rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          margin-bottom: 0.5rem;
        }
        .btn-primary {
          background: #007ACC;
          color: #ffffff;
        }
        .btn-primary:hover {
          background: #005a9e;
        }
        .btn-secondary {
          background: #404040;
          color: #ffffff;
        }
        .btn-secondary:hover {
          background: #505050;
        }
        .btn-oauth {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          background: #ffffff;
          color: #333333;
          border: 1px solid #d0d0d0;
        }
        .btn-oauth:hover {
          background: #f5f5f5;
        }
        .btn-github {
          background: #333333;
          color: #ffffff;
        }
        .btn-github:hover {
          background: #404040;
        }
        .divider {
          text-align: center;
          margin: 1.5rem 0;
          position: relative;
          color: #b0b0b0;
          font-size: 0.8rem;
        }
        .divider::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 1px;
          background: #404040;
        }
        .divider span {
          background: #2d2d2d;
          padding: 0 1rem;
        }
        .auth-switch {
          text-align: center;
          margin-top: 1.5rem;
          color: #b0b0b0;
          font-size: 0.9rem;
        }
        .auth-link {
          color: #007ACC;
          text-decoration: none;
          cursor: pointer;
        }
        .auth-link:hover {
          text-decoration: underline;
        }
        .error-message {
          background: #ff4444;
          color: #ffffff;
          padding: 0.75rem;
          border-radius: 6px;
          margin-bottom: 1rem;
          font-size: 0.9rem;
        }
        .loading {
          opacity: 0.6;
          pointer-events: none;
        }
        .mfa-code {
          text-align: center;
          font-size: 1.2rem;
          letter-spacing: 0.2rem;
        }
      </style>
    `;

    switch (this.currentView) {
      case 'signin':
        return baseStyles + this.getSignInHTML();
      case 'signup':
        return baseStyles + this.getSignUpHTML();
      case 'magic-link':
        return baseStyles + this.getMagicLinkHTML();
      case 'mfa':
        return baseStyles + this.getMFAHTML();
      default:
        return baseStyles + this.getSignInHTML();
    }
  }

  /**
   * Get sign-in form HTML
   */
  private getSignInHTML(): string {
    return `
      <div class="orium-auth">
        <div class="auth-container">
          <div class="auth-header">
            <h1 class="auth-title">Welcome to Orium</h1>
            <p class="auth-subtitle">Sign in to your collaborative coding workspace</p>
          </div>
          
          <div id="error-container"></div>
          
          <form id="signin-form">
            <div class="form-group">
              <label class="form-label" for="email">Email</label>
              <input type="email" id="email" class="form-input" required>
            </div>
            
            <div class="form-group">
              <label class="form-label" for="password">Password</label>
              <input type="password" id="password" class="form-input" required>
            </div>
            
            <button type="submit" class="btn btn-primary">Sign In</button>
          </form>
          
          <div class="divider"><span>or</span></div>
          
          <button id="google-signin" class="btn btn-oauth">
            <svg width="18" height="18" viewBox="0 0 24 24">
              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
            </svg>
            Continue with Google
          </button>
          
          <button id="github-signin" class="btn btn-github">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
            </svg>
            Continue with GitHub
          </button>
          
          <div class="divider"><span>or</span></div>
          
          <button id="magic-link" class="btn btn-secondary">Send Magic Link</button>
          
          <div class="auth-switch">
            Don't have an account? <a href="#" id="switch-signup" class="auth-link">Sign up</a>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Get sign-up form HTML
   */
  private getSignUpHTML(): string {
    return `
      <div class="orium-auth">
        <div class="auth-container">
          <div class="auth-header">
            <h1 class="auth-title">Join Orium</h1>
            <p class="auth-subtitle">Create your collaborative coding workspace</p>
          </div>
          
          <div id="error-container"></div>
          
          <form id="signup-form">
            <div class="form-group">
              <label class="form-label" for="name">Name</label>
              <input type="text" id="name" class="form-input" required>
            </div>
            
            <div class="form-group">
              <label class="form-label" for="email">Email</label>
              <input type="email" id="email" class="form-input" required>
            </div>
            
            <div class="form-group">
              <label class="form-label" for="password">Password</label>
              <input type="password" id="password" class="form-input" required minlength="8">
            </div>
            
            <button type="submit" class="btn btn-primary">Create Account</button>
          </form>
          
          <div class="auth-switch">
            Already have an account? <a href="#" id="switch-signin" class="auth-link">Sign in</a>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Get magic link form HTML
   */
  private getMagicLinkHTML(): string {
    return `
      <div class="orium-auth">
        <div class="auth-container">
          <div class="auth-header">
            <h1 class="auth-title">Magic Link</h1>
            <p class="auth-subtitle">Enter your email to receive a sign-in link</p>
          </div>
          
          <div id="error-container"></div>
          
          <form id="magic-link-form">
            <div class="form-group">
              <label class="form-label" for="email">Email</label>
              <input type="email" id="email" class="form-input" required>
            </div>
            
            <button type="submit" class="btn btn-primary">Send Magic Link</button>
          </form>
          
          <div class="auth-switch">
            <a href="#" id="back-signin" class="auth-link">← Back to sign in</a>
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Get MFA form HTML
   */
  private getMFAHTML(): string {
    return `
      <div class="orium-auth">
        <div class="auth-container">
          <div class="auth-header">
            <h1 class="auth-title">Two-Factor Authentication</h1>
            <p class="auth-subtitle">Enter the code from your authenticator app</p>
          </div>
          
          <div id="error-container"></div>
          
          <form id="mfa-form">
            <div class="form-group">
              <label class="form-label" for="mfa-code">Authentication Code</label>
              <input type="text" id="mfa-code" class="form-input mfa-code" required maxlength="6" pattern="[0-9]{6}">
            </div>
            
            <button type="submit" class="btn btn-primary">Verify</button>
          </form>
        </div>
      </div>
    `;
  }

  /**
   * Attach event listeners
   */
  private attachEventListeners(): void {
    // Sign-in form
    const signinForm = document.getElementById('signin-form');
    if (signinForm) {
      signinForm.addEventListener('submit', this.handleSignIn.bind(this));
    }

    // Sign-up form
    const signupForm = document.getElementById('signup-form');
    if (signupForm) {
      signupForm.addEventListener('submit', this.handleSignUp.bind(this));
    }

    // Magic link form
    const magicLinkForm = document.getElementById('magic-link-form');
    if (magicLinkForm) {
      magicLinkForm.addEventListener('submit', this.handleMagicLink.bind(this));
    }

    // MFA form
    const mfaForm = document.getElementById('mfa-form');
    if (mfaForm) {
      mfaForm.addEventListener('submit', this.handleMFA.bind(this));
    }

    // OAuth buttons
    const googleSignin = document.getElementById('google-signin');
    if (googleSignin) {
      googleSignin.addEventListener('click', () => this.handleOAuth('google'));
    }

    const githubSignin = document.getElementById('github-signin');
    if (githubSignin) {
      githubSignin.addEventListener('click', () => this.handleOAuth('github'));
    }

    // Magic link button
    const magicLinkBtn = document.getElementById('magic-link');
    if (magicLinkBtn) {
      magicLinkBtn.addEventListener('click', () => {
        this.currentView = 'magic-link';
        this.render();
      });
    }

    // View switching
    const switchSignup = document.getElementById('switch-signup');
    if (switchSignup) {
      switchSignup.addEventListener('click', (e) => {
        e.preventDefault();
        this.currentView = 'signup';
        this.render();
      });
    }

    const switchSignin = document.getElementById('switch-signin');
    if (switchSignin) {
      switchSignin.addEventListener('click', (e) => {
        e.preventDefault();
        this.currentView = 'signin';
        this.render();
      });
    }

    const backSignin = document.getElementById('back-signin');
    if (backSignin) {
      backSignin.addEventListener('click', (e) => {
        e.preventDefault();
        this.currentView = 'signin';
        this.render();
      });
    }
  }

  /**
   * Handle sign-in form submission
   */
  private async handleSignIn(e: Event): Promise<void> {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    await authComponent.signIn(email, password);
  }

  /**
   * Handle sign-up form submission
   */
  private async handleSignUp(e: Event): Promise<void> {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);
    const name = formData.get('name') as string;
    const email = formData.get('email') as string;
    const password = formData.get('password') as string;

    await authComponent.signUp(email, password, name);
  }

  /**
   * Handle magic link form submission
   */
  private async handleMagicLink(e: Event): Promise<void> {
    e.preventDefault();
    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);
    const email = formData.get('email') as string;

    await authComponent.signInWithMagicLink(email);
  }

  /**
   * Handle MFA form submission
   */
  private async handleMFA(e: Event): Promise<void> {
    e.preventDefault();
    if (!this.mfaData) return;

    const form = e.target as HTMLFormElement;
    const formData = new FormData(form);
    const code = formData.get('mfa-code') as string;

    await authComponent.verifyMFA(this.mfaData.factorId, this.mfaData.challengeId, code);
  }

  /**
   * Handle OAuth sign-in
   */
  private async handleOAuth(provider: 'google' | 'github'): Promise<void> {
    await authComponent.signInWithOAuth(provider);
  }

  /**
   * Update loading state
   */
  private updateLoadingState(isLoading: boolean): void {
    const container = document.querySelector('.auth-container');
    if (container) {
      if (isLoading) {
        container.classList.add('loading');
      } else {
        container.classList.remove('loading');
      }
    }
  }

  /**
   * Update error state
   */
  private updateErrorState(error: string | null): void {
    const errorContainer = document.getElementById('error-container');
    if (errorContainer) {
      if (error) {
        errorContainer.innerHTML = `<div class="error-message">${error}</div>`;
      } else {
        errorContainer.innerHTML = '';
      }
    }
  }

  /**
   * Show the authentication UI
   */
  show(): void {
    this.container.style.display = 'block';
    this.render();
  }

  /**
   * Hide the authentication UI
   */
  hide(): void {
    this.container.style.display = 'none';
  }
}
