/**
 * Jitsi Meet integration service for video conferencing
 */
import type { JitsiConfig } from '../types';
declare global {
    interface Window {
        JitsiMeetExternalAPI: any;
    }
}
export interface JitsiMeetAPI {
    dispose(): void;
    executeCommand(command: string, ...args: any[]): void;
    getVideoQuality(): string;
    isAudioMuted(): Promise<boolean>;
    isVideoMuted(): Promise<boolean>;
    addEventListener(event: string, listener: Function): void;
    removeEventListener(event: string, listener: Function): void;
}
export declare class JitsiService {
    private api;
    private container;
    private isInitialized;
    private currentConfig;
    private onReadyCallback?;
    private onParticipantJoinedCallback?;
    private onParticipantLeftCallback?;
    private onVideoConferenceJoinedCallback?;
    private onVideoConferenceLeftCallback?;
    constructor();
    /**
     * Load Jitsi Meet External API script
     */
    private loadJitsiScript;
    /**
     * Initialize Jitsi Meet conference
     */
    initialize(container: HTMLElement, config: JitsiConfig): Promise<void>;
    /**
     * Set up event listeners for Jitsi Meet events
     */
    private setupEventListeners;
    /**
     * Dispose of the Jitsi Meet instance
     */
    dispose(): Promise<void>;
    /**
     * Toggle audio mute
     */
    toggleAudio(): Promise<void>;
    /**
     * Toggle video mute
     */
    toggleVideo(): Promise<void>;
    /**
     * Start screen sharing
     */
    startScreenShare(): void;
    /**
     * End the call
     */
    hangUp(): void;
    /**
     * Set display name
     */
    setDisplayName(name: string): void;
    /**
     * Send chat message
     */
    sendChatMessage(message: string): void;
    /**
     * Check if audio is muted
     */
    isAudioMuted(): Promise<boolean>;
    /**
     * Check if video is muted
     */
    isVideoMuted(): Promise<boolean>;
    /**
     * Get current video quality
     */
    getVideoQuality(): string;
    /**
     * Set event callbacks
     */
    onReady(callback: () => void): void;
    onParticipantJoined(callback: (participant: any) => void): void;
    onParticipantLeft(callback: (participant: any) => void): void;
    onVideoConferenceJoined(callback: (participant: any) => void): void;
    onVideoConferenceLeft(callback: () => void): void;
    /**
     * Generate secure room name
     */
    static generateRoomName(sessionId: string): string;
    /**
     * Create Jitsi configuration for a session
     */
    static createConfig(sessionId: string, userInfo: {
        displayName: string;
        email?: string;
    }): JitsiConfig;
    /**
     * Check if Jitsi is supported in current browser
     */
    static isSupported(): boolean;
}
export declare const jitsiService: JitsiService;
//# sourceMappingURL=jitsiService.d.ts.map