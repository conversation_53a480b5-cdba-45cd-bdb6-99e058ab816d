/**
 * Main Orium Application Class
 * Orchestrates the entire collaborative code editor experience
 */
import { CollaborativeCodingMode } from './components/CollaborativeCodingMode';
import type { OriumSession } from './types';
export interface OriumConfig {
    websocketUrl?: string;
    enableAnalytics?: boolean;
    theme?: 'dark' | 'light';
}
export declare class OriumApp {
    private container;
    private config;
    private authUI;
    private currentMode;
    private currentSession;
    private isInitialized;
    constructor(container: HTMLElement, config?: OriumConfig);
    /**
     * Initialize the Orium application
     */
    private initialize;
    /**
     * Apply theme to the application
     */
    private applyTheme;
    /**
     * Set up authentication
     */
    private setupAuthentication;
    /**
     * Show authentication interface
     */
    private showAuthInterface;
    /**
     * Show main application interface
     */
    private showMainInterface;
    /**
     * Create main interface
     */
    private createMainInterface;
    /**
     * Attach event listeners for main interface
     */
    private attachMainInterfaceListeners;
    /**
     * Load user sessions
     */
    private loadUserSessions;
    /**
     * Render sessions list
     */
    private renderSessionsList;
    /**
     * Create new session
     */
    private createNewSession;
    /**
     * Join session
     */
    joinSession(sessionId: string): Promise<void>;
    /**
     * Show join session dialog
     */
    private showJoinSessionDialog;
    /**
     * Delete session
     */
    deleteSession(sessionId: string): Promise<void>;
    /**
     * Show loading screen
     */
    private showLoading;
    /**
     * Show error screen
     */
    private showError;
    /**
     * Get current session
     */
    getCurrentSession(): OriumSession | null;
    /**
     * Get current mode
     */
    getCurrentMode(): CollaborativeCodingMode | null;
    /**
     * Dispose of the application
     */
    dispose(): void;
}
declare global {
    interface Window {
        oriumApp: OriumApp;
    }
}
//# sourceMappingURL=OriumApp.d.ts.map