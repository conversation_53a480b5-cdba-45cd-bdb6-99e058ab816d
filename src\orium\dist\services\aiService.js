/**
 * AI service for Vibe Coding Mode
 * Supports multiple AI providers: OpenAI, Anthropic, Google, Cohere
 */
import { EncryptionService } from './encryptionService';
import { supabaseService } from './supabaseClient';
export class AIService {
    providers = new Map();
    apiKeys = new Map();
    currentProvider = 'openai';
    currentModel = 'gpt-4';
    userPassword = null;
    constructor() {
        this.initializeProviders();
    }
    /**
     * Initialize AI providers
     */
    initializeProviders() {
        const providers = [
            {
                name: 'OpenAI',
                id: 'openai',
                models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo', 'gpt-4o'],
                requiresApiKey: true
            },
            {
                name: 'Anthropic',
                id: 'anthropic',
                models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku', 'claude-2'],
                requiresApiKey: true
            },
            {
                name: 'Google',
                id: 'google',
                models: ['gemini-pro', 'gemini-pro-vision', 'palm-2'],
                requiresApiKey: true
            },
            {
                name: 'Cohere',
                id: 'cohere',
                models: ['command', 'command-light', 'command-nightly'],
                requiresApiKey: true
            }
        ];
        providers.forEach(provider => {
            this.providers.set(provider.id, provider);
        });
    }
    /**
     * Set user password for API key encryption/decryption
     */
    setUserPassword(password) {
        this.userPassword = password;
    }
    /**
     * Load API keys from Supabase
     */
    async loadApiKeys() {
        if (!this.userPassword) {
            throw new Error('User password not set for API key decryption');
        }
        const { data: apiKeys, error } = await supabaseService.getApiKeys();
        if (error) {
            console.error('Failed to load API keys:', error);
            return;
        }
        if (apiKeys) {
            for (const apiKey of apiKeys) {
                try {
                    const decryptedKey = EncryptionService.decryptApiKey(apiKey.encrypted_key, this.userPassword);
                    this.apiKeys.set(apiKey.provider, decryptedKey);
                }
                catch (error) {
                    console.error(`Failed to decrypt API key for ${apiKey.provider}:`, error);
                }
            }
        }
    }
    /**
     * Save API key securely
     */
    async saveApiKey(provider, apiKey) {
        if (!this.userPassword) {
            throw new Error('User password not set for API key encryption');
        }
        const encryptedKey = EncryptionService.encryptApiKey(apiKey, this.userPassword);
        const { error } = await supabaseService.saveApiKey(provider, encryptedKey);
        if (error) {
            throw new Error(`Failed to save API key: ${error.message}`);
        }
        this.apiKeys.set(provider, apiKey);
    }
    /**
     * Get available providers
     */
    getProviders() {
        return Array.from(this.providers.values());
    }
    /**
     * Set current provider and model
     */
    setProvider(providerId, model) {
        const provider = this.providers.get(providerId);
        if (!provider) {
            throw new Error(`Unknown provider: ${providerId}`);
        }
        this.currentProvider = providerId;
        if (model && provider.models.includes(model)) {
            this.currentModel = model;
        }
        else {
            this.currentModel = provider.models[0];
        }
    }
    /**
     * Get current provider and model
     */
    getCurrentProvider() {
        const provider = this.providers.get(this.currentProvider);
        return { provider, model: this.currentModel };
    }
    /**
     * Check if API key is available for current provider
     */
    hasApiKey(providerId) {
        const provider = providerId || this.currentProvider;
        return this.apiKeys.has(provider);
    }
    /**
     * Generate code completion
     */
    async generateCompletion(request) {
        const apiKey = this.apiKeys.get(this.currentProvider);
        if (!apiKey) {
            throw new Error(`API key not found for provider: ${this.currentProvider}`);
        }
        switch (this.currentProvider) {
            case 'openai':
                return this.callOpenAI(request, apiKey);
            case 'anthropic':
                return this.callAnthropic(request, apiKey);
            case 'google':
                return this.callGoogle(request, apiKey);
            case 'cohere':
                return this.callCohere(request, apiKey);
            default:
                throw new Error(`Unsupported provider: ${this.currentProvider}`);
        }
    }
    /**
     * Generate code suggestions
     */
    async generateCodeSuggestions(code, cursorPosition, language) {
        const prompt = this.buildCodeSuggestionPrompt(code, cursorPosition, language);
        const response = await this.generateCompletion({
            prompt,
            language,
            maxTokens: 500,
            temperature: 0.3
        });
        return this.parseCodeSuggestions(response.content, cursorPosition);
    }
    /**
     * Explain code
     */
    async explainCode(code, language) {
        const prompt = `Explain the following ${language} code in simple terms:\n\n\`\`\`${language}\n${code}\n\`\`\``;
        const response = await this.generateCompletion({
            prompt,
            language,
            maxTokens: 300,
            temperature: 0.2
        });
        return response.content;
    }
    /**
     * Fix code errors
     */
    async fixCode(code, error, language) {
        const prompt = `Fix the following ${language} code that has this error: "${error}"\n\n\`\`\`${language}\n${code}\n\`\`\`\n\nProvide only the corrected code:`;
        const response = await this.generateCompletion({
            prompt,
            language,
            maxTokens: 500,
            temperature: 0.1
        });
        return response.content;
    }
    /**
     * Generate unit tests
     */
    async generateTests(code, language) {
        const prompt = `Generate comprehensive unit tests for the following ${language} code:\n\n\`\`\`${language}\n${code}\n\`\`\``;
        const response = await this.generateCompletion({
            prompt,
            language,
            maxTokens: 800,
            temperature: 0.3
        });
        return response.content;
    }
    /**
     * Call OpenAI API
     */
    async callOpenAI(request, apiKey) {
        const response = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${apiKey}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                model: this.currentModel,
                messages: [
                    { role: 'system', content: 'You are a helpful coding assistant.' },
                    { role: 'user', content: request.prompt }
                ],
                max_tokens: request.maxTokens || 500,
                temperature: request.temperature || 0.3
            })
        });
        if (!response.ok) {
            throw new Error(`OpenAI API error: ${response.statusText}`);
        }
        const data = await response.json();
        return {
            content: data.choices[0].message.content,
            model: this.currentModel,
            provider: 'openai',
            usage: {
                promptTokens: data.usage.prompt_tokens,
                completionTokens: data.usage.completion_tokens,
                totalTokens: data.usage.total_tokens
            }
        };
    }
    /**
     * Call Anthropic API
     */
    async callAnthropic(request, apiKey) {
        const response = await fetch('https://api.anthropic.com/v1/messages', {
            method: 'POST',
            headers: {
                'x-api-key': apiKey,
                'Content-Type': 'application/json',
                'anthropic-version': '2023-06-01'
            },
            body: JSON.stringify({
                model: this.currentModel,
                max_tokens: request.maxTokens || 500,
                messages: [
                    { role: 'user', content: request.prompt }
                ]
            })
        });
        if (!response.ok) {
            throw new Error(`Anthropic API error: ${response.statusText}`);
        }
        const data = await response.json();
        return {
            content: data.content[0].text,
            model: this.currentModel,
            provider: 'anthropic',
            usage: {
                promptTokens: data.usage.input_tokens,
                completionTokens: data.usage.output_tokens,
                totalTokens: data.usage.input_tokens + data.usage.output_tokens
            }
        };
    }
    /**
     * Call Google API (placeholder)
     */
    async callGoogle(request, apiKey) {
        // Placeholder for Google Gemini API
        throw new Error('Google API integration not yet implemented');
    }
    /**
     * Call Cohere API (placeholder)
     */
    async callCohere(request, apiKey) {
        // Placeholder for Cohere API
        throw new Error('Cohere API integration not yet implemented');
    }
    /**
     * Build code suggestion prompt
     */
    buildCodeSuggestionPrompt(code, cursorPosition, language) {
        const lines = code.split('\n');
        const currentLine = lines[cursorPosition.line - 1] || '';
        const beforeCursor = currentLine.substring(0, cursorPosition.column);
        const afterCursor = currentLine.substring(cursorPosition.column);
        return `Complete the following ${language} code at the cursor position (marked with |):\n\n${beforeCursor}|${afterCursor}\n\nContext:\n${code}\n\nProvide 1-3 completion suggestions:`;
    }
    /**
     * Parse code suggestions from AI response
     */
    parseCodeSuggestions(response, cursorPosition) {
        // Simple parsing - in production, this would be more sophisticated
        const suggestions = [];
        const lines = response.split('\n');
        let currentSuggestion = '';
        let inCodeBlock = false;
        for (const line of lines) {
            if (line.startsWith('```')) {
                if (inCodeBlock) {
                    if (currentSuggestion.trim()) {
                        suggestions.push({
                            code: currentSuggestion.trim(),
                            description: 'AI generated suggestion',
                            confidence: 0.8,
                            startLine: cursorPosition.line,
                            endLine: cursorPosition.line
                        });
                    }
                    currentSuggestion = '';
                    inCodeBlock = false;
                }
                else {
                    inCodeBlock = true;
                }
            }
            else if (inCodeBlock) {
                currentSuggestion += line + '\n';
            }
        }
        return suggestions.slice(0, 3); // Return max 3 suggestions
    }
}
export const aiService = new AIService();
//# sourceMappingURL=aiService.js.map