/**
 * Encryption service for secure data handling in Orium
 * Uses AES-256-GCM for encryption and secure key derivation
 */
import * as CryptoJS from 'crypto-js';
export class EncryptionService {
    static ALGORITHM = 'AES';
    static KEY_SIZE = 256;
    static IV_SIZE = 96; // 12 bytes for GCM
    static TAG_SIZE = 128; // 16 bytes for GCM
    static ITERATIONS = 100000; // PBKDF2 iterations
    /**
     * Generate a random salt
     */
    static generateSalt() {
        return CryptoJS.lib.WordArray.random(32).toString();
    }
    /**
     * Generate a random IV
     */
    static generateIV() {
        return CryptoJS.lib.WordArray.random(12).toString();
    }
    /**
     * Derive key from password using PBKDF2
     */
    static deriveKey(password, salt) {
        return CryptoJS.PBKDF2(password, salt, {
            keySize: this.KEY_SIZE / 32,
            iterations: this.ITERATIONS,
            hasher: CryptoJS.algo.SHA256
        });
    }
    /**
     * Encrypt text using AES-256-GCM
     */
    static encrypt(plaintext, password) {
        try {
            const salt = this.generateSalt();
            const iv = this.generateIV();
            const key = this.deriveKey(password, salt);
            // Convert IV to WordArray
            const ivWordArray = CryptoJS.enc.Hex.parse(iv);
            // Encrypt using AES-GCM (simulated with AES-CTR + HMAC)
            const encrypted = CryptoJS.AES.encrypt(plaintext, key, {
                iv: ivWordArray,
                mode: CryptoJS.mode.CTR,
                padding: CryptoJS.pad.NoPadding
            });
            // Create authentication tag using HMAC
            const authTag = CryptoJS.HmacSHA256(encrypted.ciphertext.toString(), key).toString();
            // Combine salt, iv, authTag, and ciphertext
            const result = {
                salt,
                iv,
                authTag,
                ciphertext: encrypted.ciphertext.toString()
            };
            return btoa(JSON.stringify(result));
        }
        catch (error) {
            console.error('Encryption failed:', error);
            throw new Error('Failed to encrypt data');
        }
    }
    /**
     * Decrypt text using AES-256-GCM
     */
    static decrypt(encryptedData, password) {
        try {
            const data = JSON.parse(atob(encryptedData));
            const { salt, iv, authTag, ciphertext } = data;
            const key = this.deriveKey(password, salt);
            const ivWordArray = CryptoJS.enc.Hex.parse(iv);
            // Verify authentication tag
            const expectedAuthTag = CryptoJS.HmacSHA256(ciphertext, key).toString();
            if (authTag !== expectedAuthTag) {
                throw new Error('Authentication failed - data may be corrupted');
            }
            // Decrypt
            const decrypted = CryptoJS.AES.decrypt({ ciphertext: CryptoJS.enc.Hex.parse(ciphertext) }, key, {
                iv: ivWordArray,
                mode: CryptoJS.mode.CTR,
                padding: CryptoJS.pad.NoPadding
            });
            return decrypted.toString(CryptoJS.enc.Utf8);
        }
        catch (error) {
            console.error('Decryption failed:', error);
            throw new Error('Failed to decrypt data');
        }
    }
    /**
     * Generate a secure random password for session-based encryption
     */
    static generateSecurePassword() {
        return CryptoJS.lib.WordArray.random(32).toString();
    }
    /**
     * Hash password for secure storage (not for encryption)
     */
    static hashPassword(password) {
        const salt = this.generateSalt();
        const hash = CryptoJS.PBKDF2(password, salt, {
            keySize: 256 / 32,
            iterations: this.ITERATIONS,
            hasher: CryptoJS.algo.SHA256
        });
        return `${salt}:${hash.toString()}`;
    }
    /**
     * Verify password against hash
     */
    static verifyPassword(password, hash) {
        try {
            const [salt, storedHash] = hash.split(':');
            const computedHash = CryptoJS.PBKDF2(password, salt, {
                keySize: 256 / 32,
                iterations: this.ITERATIONS,
                hasher: CryptoJS.algo.SHA256
            });
            return computedHash.toString() === storedHash;
        }
        catch (error) {
            return false;
        }
    }
    /**
     * Encrypt API key for secure storage
     */
    static encryptApiKey(apiKey, userPassword) {
        return this.encrypt(apiKey, userPassword);
    }
    /**
     * Decrypt API key from secure storage
     */
    static decryptApiKey(encryptedApiKey, userPassword) {
        return this.decrypt(encryptedApiKey, userPassword);
    }
    /**
     * Encrypt chat message for end-to-end encryption
     */
    static encryptChatMessage(message, sessionKey) {
        return this.encrypt(message, sessionKey);
    }
    /**
     * Decrypt chat message
     */
    static decryptChatMessage(encryptedMessage, sessionKey) {
        return this.decrypt(encryptedMessage, sessionKey);
    }
    /**
     * Generate a session key for chat encryption
     */
    static generateSessionKey() {
        return this.generateSecurePassword();
    }
    /**
     * Encrypt file content for secure storage
     */
    static encryptFileContent(content, sessionKey) {
        return this.encrypt(content, sessionKey);
    }
    /**
     * Decrypt file content
     */
    static decryptFileContent(encryptedContent, sessionKey) {
        return this.decrypt(encryptedContent, sessionKey);
    }
    /**
     * Create a secure hash of data (for integrity checking)
     */
    static createHash(data) {
        return CryptoJS.SHA256(data).toString();
    }
    /**
     * Verify data integrity using hash
     */
    static verifyHash(data, hash) {
        return this.createHash(data) === hash;
    }
    /**
     * Generate a secure random token
     */
    static generateToken(length = 32) {
        return CryptoJS.lib.WordArray.random(length).toString();
    }
    /**
     * Encrypt data with a public key (for key exchange)
     * Note: This is a simplified implementation. In production, use proper RSA encryption.
     */
    static encryptWithPublicKey(data, publicKey) {
        // This is a placeholder - implement proper RSA encryption for key exchange
        return this.encrypt(data, publicKey);
    }
    /**
     * Decrypt data with a private key
     */
    static decryptWithPrivateKey(encryptedData, privateKey) {
        // This is a placeholder - implement proper RSA decryption for key exchange
        return this.decrypt(encryptedData, privateKey);
    }
}
/**
 * Chat encryption manager for session-based encryption
 */
export class ChatEncryptionManager {
    sessionKeys = new Map();
    /**
     * Initialize encryption for a session
     */
    initializeSession(sessionId) {
        const sessionKey = EncryptionService.generateSessionKey();
        this.sessionKeys.set(sessionId, sessionKey);
        return sessionKey;
    }
    /**
     * Get session key
     */
    getSessionKey(sessionId) {
        return this.sessionKeys.get(sessionId);
    }
    /**
     * Encrypt message for session
     */
    encryptMessage(sessionId, message) {
        const sessionKey = this.sessionKeys.get(sessionId);
        if (!sessionKey) {
            throw new Error('Session not initialized for encryption');
        }
        return EncryptionService.encryptChatMessage(message, sessionKey);
    }
    /**
     * Decrypt message for session
     */
    decryptMessage(sessionId, encryptedMessage) {
        const sessionKey = this.sessionKeys.get(sessionId);
        if (!sessionKey) {
            throw new Error('Session not initialized for encryption');
        }
        return EncryptionService.decryptChatMessage(encryptedMessage, sessionKey);
    }
    /**
     * Remove session encryption
     */
    removeSession(sessionId) {
        this.sessionKeys.delete(sessionId);
    }
}
export const chatEncryptionManager = new ChatEncryptionManager();
//# sourceMappingURL=encryptionService.js.map