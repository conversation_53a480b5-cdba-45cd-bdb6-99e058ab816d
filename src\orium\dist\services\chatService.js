/**
 * Chat service for encrypted real-time messaging
 */
import { supabaseService } from './supabaseClient';
import { chatEncryptionManager } from './encryptionService';
export class ChatService {
    sessionId = null;
    isEncryptionEnabled = true;
    messages = [];
    typingUsers = new Map();
    typingTimeouts = new Map();
    // Event callbacks
    onMessageCallback;
    onMessagesLoadedCallback;
    onTypingChangedCallback;
    onErrorCallback;
    constructor() {
        // Initialize encryption for the session when needed
    }
    /**
     * Initialize chat for a session
     */
    async initializeSession(sessionId, enableEncryption = true) {
        this.sessionId = sessionId;
        this.isEncryptionEnabled = enableEncryption;
        if (enableEncryption) {
            // Initialize encryption for this session
            chatEncryptionManager.initializeSession(sessionId);
        }
        // Load existing messages
        await this.loadMessages();
        // Subscribe to new messages
        this.subscribeToMessages();
        console.log(`Chat initialized for session: ${sessionId}, encryption: ${enableEncryption}`);
    }
    /**
     * Load existing messages from Supabase
     */
    async loadMessages() {
        if (!this.sessionId)
            return;
        try {
            const { data: messages, error } = await supabaseService.getChatMessages(this.sessionId);
            if (error) {
                console.error('Failed to load chat messages:', error);
                if (this.onErrorCallback) {
                    this.onErrorCallback('Failed to load chat messages');
                }
                return;
            }
            if (messages) {
                // Process and decrypt messages
                const processedMessages = [];
                for (const message of messages) {
                    try {
                        const processedMessage = await this.processMessage(message);
                        if (processedMessage) {
                            processedMessages.push(processedMessage);
                        }
                    }
                    catch (error) {
                        console.error('Failed to process message:', error);
                    }
                }
                this.messages = processedMessages;
                if (this.onMessagesLoadedCallback) {
                    this.onMessagesLoadedCallback(this.messages);
                }
            }
        }
        catch (error) {
            console.error('Error loading messages:', error);
            if (this.onErrorCallback) {
                this.onErrorCallback('Error loading messages');
            }
        }
    }
    /**
     * Process a message (decrypt if needed and add user info)
     */
    async processMessage(message) {
        try {
            let content = message.content;
            // Decrypt if message is encrypted
            if (message.is_encrypted && this.isEncryptionEnabled && this.sessionId) {
                try {
                    content = chatEncryptionManager.decryptMessage(this.sessionId, message.content);
                }
                catch (error) {
                    console.error('Failed to decrypt message:', error);
                    content = '[Encrypted message - unable to decrypt]';
                }
            }
            // Get user info (this would typically come from a user cache or API)
            const user = {
                id: message.user_id,
                name: 'User', // This should be fetched from user data
                isOnline: true
            };
            return {
                ...message,
                content,
                user
            };
        }
        catch (error) {
            console.error('Error processing message:', error);
            return null;
        }
    }
    /**
     * Subscribe to new messages
     */
    subscribeToMessages() {
        if (!this.sessionId)
            return;
        supabaseService.subscribeToChat(this.sessionId, async (payload) => {
            if (payload.eventType === 'INSERT') {
                const newMessage = payload.new;
                const processedMessage = await this.processMessage(newMessage);
                if (processedMessage) {
                    this.messages.push(processedMessage);
                    if (this.onMessageCallback) {
                        this.onMessageCallback(processedMessage);
                    }
                }
            }
        });
    }
    /**
     * Send a message
     */
    async sendMessage(content) {
        if (!this.sessionId) {
            throw new Error('Chat not initialized');
        }
        if (!content.trim()) {
            return;
        }
        try {
            let messageContent = content;
            let isEncrypted = false;
            // Encrypt message if encryption is enabled
            if (this.isEncryptionEnabled) {
                try {
                    messageContent = chatEncryptionManager.encryptMessage(this.sessionId, content);
                    isEncrypted = true;
                }
                catch (error) {
                    console.error('Failed to encrypt message:', error);
                    // Send unencrypted if encryption fails
                }
            }
            // Send message to Supabase
            const { error } = await supabaseService.sendChatMessage(this.sessionId, messageContent, isEncrypted);
            if (error) {
                throw new Error(error.message);
            }
            console.log('Message sent successfully');
        }
        catch (error) {
            console.error('Failed to send message:', error);
            if (this.onErrorCallback) {
                this.onErrorCallback('Failed to send message');
            }
            throw error;
        }
    }
    /**
     * Send typing indicator
     */
    sendTypingStart() {
        // This would be sent through the collaboration service WebSocket
        // The collaboration service handles typing indicators
    }
    /**
     * Send typing stop indicator
     */
    sendTypingStop() {
        // This would be sent through the collaboration service WebSocket
    }
    /**
     * Handle typing start from another user
     */
    handleTypingStart(userId, userName) {
        // Clear existing timeout for this user
        const existingTimeout = this.typingTimeouts.get(userId);
        if (existingTimeout) {
            clearTimeout(existingTimeout);
        }
        // Add user to typing list
        this.typingUsers.set(userId, { id: userId, name: userName });
        // Set timeout to remove user from typing list
        const timeout = setTimeout(() => {
            this.typingUsers.delete(userId);
            this.typingTimeouts.delete(userId);
            this.notifyTypingChanged();
        }, 3000); // Remove after 3 seconds of inactivity
        this.typingTimeouts.set(userId, timeout);
        this.notifyTypingChanged();
    }
    /**
     * Handle typing stop from another user
     */
    handleTypingStop(userId) {
        const timeout = this.typingTimeouts.get(userId);
        if (timeout) {
            clearTimeout(timeout);
            this.typingTimeouts.delete(userId);
        }
        this.typingUsers.delete(userId);
        this.notifyTypingChanged();
    }
    /**
     * Notify about typing changes
     */
    notifyTypingChanged() {
        if (this.onTypingChangedCallback) {
            this.onTypingChangedCallback(Array.from(this.typingUsers.values()));
        }
    }
    /**
     * Get all messages
     */
    getMessages() {
        return [...this.messages];
    }
    /**
     * Get typing users
     */
    getTypingUsers() {
        return Array.from(this.typingUsers.values());
    }
    /**
     * Clear all messages (local only)
     */
    clearMessages() {
        this.messages = [];
    }
    /**
     * Disconnect from chat
     */
    disconnect() {
        // Clear typing timeouts
        this.typingTimeouts.forEach(timeout => clearTimeout(timeout));
        this.typingTimeouts.clear();
        this.typingUsers.clear();
        // Remove encryption session
        if (this.sessionId && this.isEncryptionEnabled) {
            chatEncryptionManager.removeSession(this.sessionId);
        }
        this.sessionId = null;
        this.messages = [];
        console.log('Chat disconnected');
    }
    /**
     * Set event callbacks
     */
    onMessage(callback) {
        this.onMessageCallback = callback;
    }
    onMessagesLoaded(callback) {
        this.onMessagesLoadedCallback = callback;
    }
    onTypingChanged(callback) {
        this.onTypingChangedCallback = callback;
    }
    onError(callback) {
        this.onErrorCallback = callback;
    }
    /**
     * Format message for display
     */
    static formatMessage(message) {
        const timestamp = new Date(message.created_at).toLocaleTimeString();
        return `[${timestamp}] ${message.user.name}: ${message.content}`;
    }
    /**
     * Check if message contains code
     */
    static isCodeMessage(content) {
        return content.includes('```') || content.includes('`');
    }
    /**
     * Extract code blocks from message
     */
    static extractCodeBlocks(content) {
        const codeBlocks = [];
        const regex = /```(\w+)?\n([\s\S]*?)```/g;
        let match;
        while ((match = regex.exec(content)) !== null) {
            codeBlocks.push({
                language: match[1] || 'plaintext',
                code: match[2].trim()
            });
        }
        return codeBlocks;
    }
}
export const chatService = new ChatService();
//# sourceMappingURL=chatService.js.map