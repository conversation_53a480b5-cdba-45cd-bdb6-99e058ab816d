/**
 * Supabase client service for Orium
 */
import { User, Session } from '@supabase/supabase-js';
import type { OriumSession, OriumDocument, ChatMessage, UserPresence, ApiKey } from '../types';
export declare class SupabaseService {
    private client;
    private currentUser;
    private currentSession;
    constructor();
    private initializeAuth;
    signUp(email: string, password: string, metadata?: {
        name?: string;
    }): Promise<{
        data: {
            user: User | null;
            session: Session | null;
        } | {
            user: null;
            session: null;
        };
        error: import("@supabase/supabase-js").AuthError | null;
    }>;
    signIn(email: string, password: string): Promise<{
        data: {
            user: User;
            session: Session;
            weakPassword?: import("@supabase/supabase-js").WeakPassword;
        } | {
            user: null;
            session: null;
            weakPassword?: null;
        };
        error: import("@supabase/supabase-js").AuthError | null;
    }>;
    signInWithOAuth(provider: 'google' | 'github'): Promise<{
        data: {
            provider: import("@supabase/supabase-js").Provider;
            url: string;
        } | {
            provider: import("@supabase/supabase-js").Provider;
            url: null;
        };
        error: import("@supabase/supabase-js").AuthError | null;
    }>;
    signInWithOtp(email: string): Promise<{
        data: {
            user: null;
            session: null;
            messageId?: string | null;
        } | {
            user: null;
            session: null;
            messageId?: string | null;
        };
        error: import("@supabase/supabase-js").AuthError | null;
    }>;
    signOut(): Promise<{
        error: import("@supabase/supabase-js").AuthError | null;
    }>;
    enableMFA(): Promise<{
        data: {
            id: string;
            type: "totp";
            totp: {
                qr_code: string;
                secret: string;
                uri: string;
            };
            friendly_name?: string;
        } | null;
        error: import("@supabase/supabase-js").AuthError | null;
    }>;
    verifyMFA(factorId: string, challengeId: string, code: string): Promise<{
        data: {
            access_token: string;
            token_type: string;
            expires_in: number;
            refresh_token: string;
            user: User;
        } | null;
        error: import("@supabase/supabase-js").AuthError | null;
    }>;
    getCurrentUser(): User | null;
    getCurrentSession(): Session | null;
    createSession(name: string, mode: 'collaborative' | 'vibe'): Promise<{
        data: OriumSession | null;
        error: any;
    }>;
    getSession(sessionId: string): Promise<{
        data: OriumSession | null;
        error: any;
    }>;
    getUserSessions(): Promise<{
        data: OriumSession[] | null;
        error: any;
    }>;
    createDocument(sessionId: string, name: string, content: string, language: string): Promise<{
        data: OriumDocument | null;
        error: any;
    }>;
    getSessionDocuments(sessionId: string): Promise<{
        data: OriumDocument[] | null;
        error: any;
    }>;
    sendChatMessage(sessionId: string, content: string, isEncrypted?: boolean): Promise<{
        data: ChatMessage | null;
        error: any;
    }>;
    getChatMessages(sessionId: string): Promise<{
        data: ChatMessage[] | null;
        error: any;
    }>;
    updatePresence(sessionId: string, presence: Partial<UserPresence>): Promise<{
        error: any;
    }>;
    getSessionPresence(sessionId: string): Promise<{
        data: UserPresence[] | null;
        error: any;
    }>;
    subscribeToSession(sessionId: string, callback: (payload: any) => void): import("@supabase/supabase-js").RealtimeChannel;
    subscribeToChat(sessionId: string, callback: (payload: any) => void): import("@supabase/supabase-js").RealtimeChannel;
    subscribeToPresence(sessionId: string, callback: (payload: any) => void): import("@supabase/supabase-js").RealtimeChannel;
    saveApiKey(provider: string, encryptedKey: string): Promise<{
        data: ApiKey | null;
        error: any;
    }>;
    getApiKeys(): Promise<{
        data: ApiKey[] | null;
        error: any;
    }>;
}
export declare const supabaseService: SupabaseService;
//# sourceMappingURL=supabaseClient.d.ts.map