-- Orium Database Schema for Supabase
-- This file contains the SQL schema for the Orium collaborative code editor

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- <PERSON>reate custom types
CREATE TYPE session_mode AS ENUM ('collaborative', 'vibe');
CREATE TYPE api_provider AS ENUM ('openai', 'anthropic', 'google', 'cohere');

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Sessions table
CREATE TABLE public.sessions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  owner_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  mode session_mode NOT NULL DEFAULT 'collaborative',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Documents table
CREATE TABLE public.documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id UUID REFERENCES public.sessions(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  content TEXT DEFAULT '',
  language TEXT DEFAULT 'plaintext',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat messages table
CREATE TABLE public.chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  session_id UUID REFERENCES public.sessions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  is_encrypted BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User presence table
CREATE TABLE public.user_presence (
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  session_id UUID REFERENCES public.sessions(id) ON DELETE CASCADE,
  cursor_position JSONB,
  selection JSONB,
  color TEXT NOT NULL DEFAULT '#007ACC',
  last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (user_id, session_id)
);

-- API keys table (encrypted)
CREATE TABLE public.api_keys (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  provider api_provider NOT NULL,
  encrypted_key TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, provider)
);

-- Session participants table
CREATE TABLE public.session_participants (
  session_id UUID REFERENCES public.sessions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  PRIMARY KEY (session_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX idx_sessions_owner_id ON public.sessions(owner_id);
CREATE INDEX idx_sessions_is_active ON public.sessions(is_active);
CREATE INDEX idx_documents_session_id ON public.documents(session_id);
CREATE INDEX idx_chat_messages_session_id ON public.chat_messages(session_id);
CREATE INDEX idx_chat_messages_created_at ON public.chat_messages(created_at);
CREATE INDEX idx_user_presence_session_id ON public.user_presence(session_id);
CREATE INDEX idx_user_presence_last_seen ON public.user_presence(last_seen);
CREATE INDEX idx_api_keys_user_id ON public.api_keys(user_id);
CREATE INDEX idx_session_participants_session_id ON public.session_participants(session_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_presence ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.session_participants ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- Users: Users can only see and update their own profile
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.users
  FOR INSERT WITH CHECK (auth.uid() = id);

-- Sessions: Users can see sessions they own or participate in
CREATE POLICY "Users can view own sessions" ON public.sessions
  FOR SELECT USING (
    auth.uid() = owner_id OR 
    auth.uid() IN (
      SELECT user_id FROM public.session_participants 
      WHERE session_id = sessions.id
    )
  );

CREATE POLICY "Users can create sessions" ON public.sessions
  FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "Session owners can update sessions" ON public.sessions
  FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "Session owners can delete sessions" ON public.sessions
  FOR DELETE USING (auth.uid() = owner_id);

-- Documents: Users can access documents in sessions they participate in
CREATE POLICY "Users can view session documents" ON public.documents
  FOR SELECT USING (
    session_id IN (
      SELECT id FROM public.sessions 
      WHERE owner_id = auth.uid() OR 
      id IN (
        SELECT session_id FROM public.session_participants 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can create documents in their sessions" ON public.documents
  FOR INSERT WITH CHECK (
    session_id IN (
      SELECT id FROM public.sessions 
      WHERE owner_id = auth.uid() OR 
      id IN (
        SELECT session_id FROM public.session_participants 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can update documents in their sessions" ON public.documents
  FOR UPDATE USING (
    session_id IN (
      SELECT id FROM public.sessions 
      WHERE owner_id = auth.uid() OR 
      id IN (
        SELECT session_id FROM public.session_participants 
        WHERE user_id = auth.uid()
      )
    )
  );

-- Chat messages: Users can see messages in sessions they participate in
CREATE POLICY "Users can view session chat messages" ON public.chat_messages
  FOR SELECT USING (
    session_id IN (
      SELECT id FROM public.sessions 
      WHERE owner_id = auth.uid() OR 
      id IN (
        SELECT session_id FROM public.session_participants 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can send chat messages" ON public.chat_messages
  FOR INSERT WITH CHECK (
    auth.uid() = user_id AND
    session_id IN (
      SELECT id FROM public.sessions 
      WHERE owner_id = auth.uid() OR 
      id IN (
        SELECT session_id FROM public.session_participants 
        WHERE user_id = auth.uid()
      )
    )
  );

-- User presence: Users can see presence in sessions they participate in
CREATE POLICY "Users can view session presence" ON public.user_presence
  FOR SELECT USING (
    session_id IN (
      SELECT id FROM public.sessions 
      WHERE owner_id = auth.uid() OR 
      id IN (
        SELECT session_id FROM public.session_participants 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Users can update own presence" ON public.user_presence
  FOR ALL USING (auth.uid() = user_id);

-- API keys: Users can only access their own API keys
CREATE POLICY "Users can manage own API keys" ON public.api_keys
  FOR ALL USING (auth.uid() = user_id);

-- Session participants: Users can see participants in sessions they're part of
CREATE POLICY "Users can view session participants" ON public.session_participants
  FOR SELECT USING (
    session_id IN (
      SELECT id FROM public.sessions 
      WHERE owner_id = auth.uid() OR 
      id IN (
        SELECT session_id FROM public.session_participants 
        WHERE user_id = auth.uid()
      )
    )
  );

CREATE POLICY "Session owners can manage participants" ON public.session_participants
  FOR ALL USING (
    session_id IN (
      SELECT id FROM public.sessions 
      WHERE owner_id = auth.uid()
    )
  );

-- Functions and triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_sessions_updated_at BEFORE UPDATE ON public.sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON public.documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_api_keys_updated_at BEFORE UPDATE ON public.api_keys
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically create user profile on signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name, avatar_url)
  VALUES (
    NEW.id,
    NEW.email,
    NEW.raw_user_meta_data->>'name',
    NEW.raw_user_meta_data->>'avatar_url'
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on signup
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
