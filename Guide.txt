Project Prompt: Orium – Secure AI-Powered Real-Time Collaborative Code Editor
Goal: Build Orium, a secure, production-ready code editor based on VS Code with real-time collaboration, video conferencing, AI coding assistant, and two working modes.

Core Requirements
1. Authentication & User Management 

Use supabase for sign-up/sign-in.

Support email/password, OAuth (Google, GitHub), and passwordless links.

Enable MFA (multi-factor authentication).

2. Operational Modes

- Collaborative Coding Mode

Multiple users edit the same code file in real time.

Show live cursors with color per user.

Encrypted chat sidebar with code formatting.

Track changes with collaborative undo/redo.

- Vibe Coding Mode with AI

AI-assisted coding suggestions.

Optional ambient background animations.

Soft glow cursor effects.

Ability to toggle vibe effects.

3. Video Conferencing (Jitsi)

Integrate Jitsi Meet for live video calls.

Place video panel at bottom right.

Make video panel collapsible and resizable.

Use secure token-based meeting rooms.

4. Editor UI & Theming

Apply solid dark theme (WCAG contrast compliant).

Enhance left sidebar: sleek rounded icons, spacing improvements.

Smooth hover animations on icons and file items.

Collapsible panels with slide animations.

5. AI Model API Key Management

Add Settings Panel for API keys.

Encrypt keys with AES-256 in Supabase.

Allow users to select and switch AI models (OpenAI, Anthropic, etc.).

6. Real-Time Backend & Data Handling

Use Yjs + WebSockets (over WSS) for live collaboration.

Supabase for file storage, user data, and presence tracking.

Row-Level Security (RLS) for user data isolation.

7. Security & Production Readiness

All connections encrypted (HTTPS, WSS).

End-to-end encrypted chat, document sync, and video calls.

Zero-trust: sensitive data never stored in plain text.

Error handling with retry logic and offline sync.

User Flow
User opens Orium → Clerk sign-up/sign-in → MFA check.

User chooses mode: Collaborative or Vibe Coding.

Collaborative Mode:

Joins/creates secure session.

Video call starts (Jitsi).

Real-time editing + chat + presence.

Vibe Mode:

AI assistant active.

Optional ambient visuals.

User can switch modes without losing current session.

Technical Stack
Base: VS Code clone (Electron + Monaco)

Auth: Clerk

Realtime: Yjs + WebSockets

Backend: Supabase (RLS, encrypted storage)

Video: Jitsi Meet API (E2EE)

UI: TailwindCSS + Enhanced VS Code components

Final Deliverable:
A secure, scalable, real-time collaborative code editor with two modes:

Collaborative Mode (multi-user coding with video + chat)

Vibe Coding Mode (AI-powered, ambient effects)