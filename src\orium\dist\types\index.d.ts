/**
 * Type definitions for Orium
 */
export interface OriumUser {
    id: string;
    email: string;
    name?: string;
    avatar_url?: string;
    created_at: string;
    updated_at: string;
}
export interface OriumSession {
    id: string;
    name: string;
    owner_id: string;
    created_at: string;
    updated_at: string;
    is_active: boolean;
    mode: 'collaborative' | 'vibe';
}
export interface OriumDocument {
    id: string;
    session_id: string;
    name: string;
    content: string;
    language: string;
    created_at: string;
    updated_at: string;
}
export interface ChatMessage {
    id: string;
    session_id: string;
    user_id: string;
    content: string;
    is_encrypted: boolean;
    created_at: string;
}
export interface UserPresence {
    user_id: string;
    session_id: string;
    cursor_position?: {
        line: number;
        column: number;
    };
    selection?: {
        start: {
            line: number;
            column: number;
        };
        end: {
            line: number;
            column: number;
        };
    };
    color: string;
    last_seen: string;
}
export interface ApiKey {
    id: string;
    user_id: string;
    provider: 'openai' | 'anthropic' | 'google' | 'cohere';
    encrypted_key: string;
    created_at: string;
    updated_at: string;
}
export interface OriumMode {
    type: 'collaborative' | 'vibe';
    settings: {
        collaborative?: {
            showCursors: boolean;
            enableChat: boolean;
            enableVideo: boolean;
        };
        vibe?: {
            enableAI: boolean;
            enableAnimations: boolean;
            enableCursorEffects: boolean;
            aiProvider: string;
        };
    };
}
export interface JitsiConfig {
    domain: string;
    roomName: string;
    jwt?: string;
    userInfo: {
        displayName: string;
        email?: string;
    };
}
//# sourceMappingURL=index.d.ts.map