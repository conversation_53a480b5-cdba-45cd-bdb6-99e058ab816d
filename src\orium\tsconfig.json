{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "node", "lib": ["ES2022", "DOM", "DOM.Iterable"], "outDir": "./dist", "rootDir": "./", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "resolveJsonModule": true, "isolatedModules": true, "verbatimModuleSyntax": false, "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "useDefineForClassFields": true, "types": ["node", "jest"], "typeRoots": ["./node_modules/@types", "./types"]}, "include": ["**/*.ts", "**/*.js", "types/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}