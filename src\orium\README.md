# Orium - Secure AI-Powered Real-Time Collaborative Code Editor

Orium is a modern, secure collaborative code editor built on VS Code with real-time collaboration, video conferencing, AI coding assistance, and two distinct working modes.

## 🚀 Features

### 🔐 Authentication & Security
- **Supabase Authentication**: Email/password, OAuth (Google, GitHub), passwordless magic links
- **Multi-Factor Authentication (MFA)**: TOTP-based 2FA for enhanced security
- **End-to-End Encryption**: AES-256 encrypted chat and document sync
- **Row-Level Security**: Supabase RLS for data isolation
- **Zero-Trust Architecture**: Sensitive data never stored in plain text

### 🤝 Collaborative Coding Mode
- **Real-Time Multi-User Editing**: Powered by Yjs + WebSockets
- **Live Cursors & Selections**: See where other users are working
- **Encrypted Chat Sidebar**: Secure messaging with code formatting
- **Collaborative Undo/Redo**: Synchronized editing history
- **User Presence Tracking**: See who's online and active

### 🎨 Vibe Coding Mode with AI
- **AI-Assisted Coding**: Support for OpenAI, Anthropic, Google, Cohere
- **Ambient Animations**: Optional background effects for focus
- **Cursor Effects**: Soft glow and visual enhancements
- **Intelligent Suggestions**: Context-aware code completion
- **Code Explanation**: AI-powered code analysis and documentation

### 📹 Video Conferencing
- **Jitsi Meet Integration**: Secure, encrypted video calls
- **Collapsible Video Panel**: Bottom-right positioned, resizable
- **Token-Based Security**: Secure meeting rooms
- **Screen Sharing**: Share your screen during collaboration

### 🎨 Enhanced UI & Theming
- **Dark Theme**: WCAG contrast compliant design
- **Smooth Animations**: Hover effects and transitions
- **Enhanced Sidebar**: Sleek rounded icons and improved spacing
- **Responsive Design**: Works on desktop and mobile devices

### 🔑 AI Model Management
- **Secure API Key Storage**: AES-256 encrypted in Supabase
- **Multiple Providers**: Switch between AI models seamlessly
- **User-Controlled**: Manage your own API keys securely

## 🏗️ Architecture

### Tech Stack
- **Base**: VS Code clone (Monaco Editor + TypeScript)
- **Authentication**: Supabase Auth
- **Real-time**: Yjs + WebSockets (WSS)
- **Backend**: Supabase with Row-Level Security
- **Video**: Jitsi Meet API with E2EE
- **UI**: TailwindCSS + Enhanced VS Code components
- **AI**: Multiple provider support (OpenAI, Anthropic, etc.)

### Security Features
- **HTTPS/WSS**: All connections encrypted in transit
- **End-to-End Encryption**: Chat and document sync
- **API Key Encryption**: AES-256 with user-controlled keys
- **Row-Level Security**: Database-level access control
- **JWT Authentication**: Secure session management

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 9+
- Supabase account and project
- (Optional) AI provider API keys

### Installation

1. **Clone and setup**:
```bash
cd src/orium
npm install
```

2. **Configure Supabase**:
   - Update `src/orium/config/supabase.ts` with your credentials
   - Run the database schema from `src/orium/database/schema.sql`

3. **Start the development server**:
```bash
npm run start:dev
```

This will:
- Build the TypeScript code
- Start the WebSocket server on port 1234
- Serve the app on http://localhost:3000
- Watch for changes and rebuild automatically

### Production Build

```bash
npm run build
npm start
```

## 📁 Project Structure

```
src/orium/
├── components/           # React-like components
│   ├── AuthComponent.ts     # Authentication logic
│   └── CollaborativeCodingMode.ts  # Main collaborative interface
├── services/            # Core services
│   ├── supabaseClient.ts    # Supabase integration
│   ├── collaborationService.ts  # Yjs collaboration
│   ├── encryptionService.ts     # AES encryption
│   ├── jitsiService.ts         # Video conferencing
│   ├── aiService.ts           # AI model integration
│   └── chatService.ts         # Encrypted messaging
├── ui/                  # UI components
│   └── AuthUI.ts           # Authentication interface
├── server/              # Backend services
│   └── websocketServer.ts   # WebSocket server
├── config/              # Configuration
│   └── supabase.ts         # Supabase config
├── database/            # Database schema
│   └── schema.sql          # Supabase schema
├── types/               # TypeScript types
│   └── index.ts            # Type definitions
├── OriumApp.ts          # Main application class
├── index.html           # Entry point
└── package.json         # Dependencies
```

## 🔧 Configuration

### Supabase Setup

1. Create a new Supabase project
2. Run the SQL schema from `database/schema.sql`
3. Enable authentication providers (Google, GitHub)
4. Update the configuration in `config/supabase.ts`

### Environment Variables

Create a `.env` file:
```env
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
WEBSOCKET_PORT=1234
```

### AI Provider Setup

1. Get API keys from your preferred providers:
   - OpenAI: https://platform.openai.com/api-keys
   - Anthropic: https://console.anthropic.com/
   - Google: https://makersuite.google.com/app/apikey
   - Cohere: https://dashboard.cohere.ai/api-keys

2. Add them securely through the Orium settings panel

## 🔒 Security Considerations

### Data Protection
- All API keys are encrypted with AES-256 before storage
- Chat messages can be end-to-end encrypted
- User data is isolated with Row-Level Security
- No sensitive data is logged or stored in plain text

### Network Security
- All connections use HTTPS/WSS encryption
- Jitsi Meet calls are end-to-end encrypted
- WebSocket connections are authenticated
- CORS policies restrict unauthorized access

### Authentication
- Multi-factor authentication supported
- OAuth integration with major providers
- Passwordless authentication available
- Session management with JWT tokens

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

### Development Guidelines
- Follow TypeScript best practices
- Add tests for new features
- Update documentation
- Ensure security best practices

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **VS Code Team**: For the amazing Monaco Editor
- **Yjs Team**: For the excellent collaborative editing framework
- **Supabase Team**: For the powerful backend-as-a-service platform
- **Jitsi Team**: For the open-source video conferencing solution

## 📞 Support

- **Documentation**: [docs.orium.dev](https://docs.orium.dev)
- **Issues**: [GitHub Issues](https://github.com/orium/orium/issues)
- **Discussions**: [GitHub Discussions](https://github.com/orium/orium/discussions)
- **Email**: <EMAIL>

---

**Built with ❤️ by the Orium Team**
