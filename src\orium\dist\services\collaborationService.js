/**
 * Collaboration service using Yjs for real-time collaborative editing
 */
import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';
import { MonacoBinding } from 'y-monaco';
import { supabaseService } from './supabaseClient';
export class CollaborationService {
    ydoc;
    provider = null;
    binding = null;
    sessionId = null;
    currentUser = null;
    users = new Map();
    presenceUpdateInterval = null;
    // Event callbacks
    onUsersChangedCallback;
    onCursorChangedCallback;
    onSelectionChangedCallback;
    constructor() {
        this.ydoc = new Y.Doc();
    }
    /**
     * Initialize collaboration for a session
     */
    async initializeSession(sessionId, websocketUrl = 'ws://localhost:1234') {
        this.sessionId = sessionId;
        // Get current user info
        const user = supabaseService.getCurrentUser();
        if (!user) {
            throw new Error('User not authenticated');
        }
        this.currentUser = {
            id: user.id,
            name: user.user_metadata?.name || user.email || 'Anonymous',
            color: this.generateUserColor(user.id)
        };
        // Create WebSocket provider with user info
        const wsUrl = `${websocketUrl}/collaboration?room=${sessionId}&userId=${user.id}&userName=${encodeURIComponent(this.currentUser.name)}`;
        this.provider = new WebsocketProvider(wsUrl, sessionId, this.ydoc);
        // Set up awareness (user presence)
        this.provider.awareness.setLocalStateField('user', this.currentUser);
        // Listen for awareness changes
        this.provider.awareness.on('change', this.handleAwarenessChange.bind(this));
        // Listen for WebSocket messages
        this.provider.ws?.addEventListener('message', this.handleWebSocketMessage.bind(this));
        // Start presence updates
        this.startPresenceUpdates();
        console.log(`Collaboration initialized for session: ${sessionId}`);
    }
    /**
     * Bind Monaco editor to Yjs document
     */
    bindEditor(monacoEditor) {
        if (!this.provider) {
            throw new Error('Collaboration not initialized');
        }
        const ytext = this.ydoc.getText('monaco');
        this.binding = new MonacoBinding(ytext, monacoEditor.getModel(), new Set([monacoEditor]), this.provider.awareness);
        // Listen for cursor and selection changes
        monacoEditor.onDidChangeCursorPosition((e) => {
            this.handleCursorChange(e.position);
        });
        monacoEditor.onDidChangeCursorSelection((e) => {
            this.handleSelectionChange(e.selection);
        });
        console.log('Monaco editor bound to collaboration');
    }
    /**
     * Disconnect from collaboration
     */
    disconnect() {
        if (this.presenceUpdateInterval) {
            clearInterval(this.presenceUpdateInterval);
            this.presenceUpdateInterval = null;
        }
        if (this.binding) {
            this.binding.destroy();
            this.binding = null;
        }
        if (this.provider) {
            this.provider.destroy();
            this.provider = null;
        }
        this.users.clear();
        this.sessionId = null;
        this.currentUser = null;
        console.log('Collaboration disconnected');
    }
    /**
     * Get current document content
     */
    getDocumentContent() {
        const ytext = this.ydoc.getText('monaco');
        return ytext.toString();
    }
    /**
     * Set document content (use carefully - this will overwrite)
     */
    setDocumentContent(content) {
        const ytext = this.ydoc.getText('monaco');
        ytext.delete(0, ytext.length);
        ytext.insert(0, content);
    }
    /**
     * Get list of connected users
     */
    getConnectedUsers() {
        return Array.from(this.users.values());
    }
    /**
     * Set callback for when users change
     */
    onUsersChanged(callback) {
        this.onUsersChangedCallback = callback;
    }
    /**
     * Set callback for cursor changes
     */
    onCursorChanged(callback) {
        this.onCursorChangedCallback = callback;
    }
    /**
     * Set callback for selection changes
     */
    onSelectionChanged(callback) {
        this.onSelectionChangedCallback = callback;
    }
    /**
     * Handle awareness changes (user presence)
     */
    handleAwarenessChange() {
        if (!this.provider)
            return;
        const states = this.provider.awareness.getStates();
        this.users.clear();
        states.forEach((state, clientId) => {
            if (state.user && clientId !== this.provider.awareness.clientID) {
                this.users.set(state.user.id, state.user);
            }
        });
        // Notify about user changes
        if (this.onUsersChangedCallback) {
            this.onUsersChangedCallback(this.getConnectedUsers());
        }
    }
    /**
     * Handle WebSocket messages
     */
    handleWebSocketMessage(event) {
        try {
            const message = JSON.parse(event.data);
            switch (message.type) {
                case 'welcome':
                    console.log(`Connected to room ${message.roomName}, ${message.connectedUsers} users online`);
                    break;
                case 'user-joined':
                    console.log(`User ${message.userName} joined the session`);
                    break;
                case 'user-left':
                    console.log(`User ${message.userName} left the session`);
                    break;
                case 'chat-message':
                    // Handle chat messages (will be implemented in chat service)
                    break;
                case 'cursor-update':
                    if (this.onCursorChangedCallback) {
                        this.onCursorChangedCallback(message.userId, message.cursor);
                    }
                    break;
                case 'selection-update':
                    if (this.onSelectionChangedCallback) {
                        this.onSelectionChangedCallback(message.userId, message.selection);
                    }
                    break;
                default:
                    console.log('Unknown WebSocket message:', message);
            }
        }
        catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    }
    /**
     * Handle cursor position changes
     */
    handleCursorChange(position) {
        if (!this.currentUser || !this.provider)
            return;
        const cursor = {
            line: position.lineNumber,
            column: position.column
        };
        this.currentUser.cursor = cursor;
        this.provider.awareness.setLocalStateField('user', this.currentUser);
        // Update presence in Supabase
        this.updateSupabasePresence();
        // Notify about cursor change
        if (this.onCursorChangedCallback) {
            this.onCursorChangedCallback(this.currentUser.id, cursor);
        }
    }
    /**
     * Handle selection changes
     */
    handleSelectionChange(selection) {
        if (!this.currentUser || !this.provider)
            return;
        const selectionData = {
            start: {
                line: selection.startLineNumber,
                column: selection.startColumn
            },
            end: {
                line: selection.endLineNumber,
                column: selection.endColumn
            }
        };
        this.currentUser.selection = selectionData;
        this.provider.awareness.setLocalStateField('user', this.currentUser);
        // Update presence in Supabase
        this.updateSupabasePresence();
        // Notify about selection change
        if (this.onSelectionChangedCallback) {
            this.onSelectionChangedCallback(this.currentUser.id, selectionData);
        }
    }
    /**
     * Update presence in Supabase
     */
    async updateSupabasePresence() {
        if (!this.sessionId || !this.currentUser)
            return;
        const presence = {
            cursor_position: this.currentUser.cursor,
            selection: this.currentUser.selection,
            color: this.currentUser.color
        };
        await supabaseService.updatePresence(this.sessionId, presence);
    }
    /**
     * Start periodic presence updates
     */
    startPresenceUpdates() {
        this.presenceUpdateInterval = setInterval(() => {
            this.updateSupabasePresence();
        }, 5000); // Update every 5 seconds
    }
    /**
     * Generate a consistent color for a user based on their ID
     */
    generateUserColor(userId) {
        const colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
            '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
            '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2'
        ];
        let hash = 0;
        for (let i = 0; i < userId.length; i++) {
            hash = userId.charCodeAt(i) + ((hash << 5) - hash);
        }
        return colors[Math.abs(hash) % colors.length];
    }
    /**
     * Apply operation to document (for undo/redo)
     */
    applyOperation(operation) {
        // This would be implemented based on the specific operation format
        // For now, we rely on Yjs's built-in undo/redo
        console.log('Applying operation:', operation);
    }
    /**
     * Get undo manager for collaborative undo/redo
     */
    getUndoManager() {
        const ytext = this.ydoc.getText('monaco');
        return new Y.UndoManager(ytext);
    }
    /**
     * Send custom message through WebSocket
     */
    sendMessage(message) {
        if (this.provider?.ws && this.provider.ws.readyState === WebSocket.OPEN) {
            this.provider.ws.send(JSON.stringify(message));
        }
    }
    /**
     * Send chat message
     */
    sendChatMessage(content) {
        this.sendMessage({
            type: 'chat-message',
            content
        });
    }
    /**
     * Send typing indicator
     */
    sendTypingStart() {
        this.sendMessage({
            type: 'typing-start'
        });
    }
    /**
     * Send typing stop indicator
     */
    sendTypingStop() {
        this.sendMessage({
            type: 'typing-stop'
        });
    }
    /**
     * Send cursor position update
     */
    sendCursorUpdate(cursor) {
        this.sendMessage({
            type: 'cursor-update',
            cursor
        });
    }
    /**
     * Send selection update
     */
    sendSelectionUpdate(selection) {
        this.sendMessage({
            type: 'selection-update',
            selection
        });
    }
}
//# sourceMappingURL=collaborationService.js.map