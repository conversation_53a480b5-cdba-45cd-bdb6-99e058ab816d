/**
 * Chat service for encrypted real-time messaging
 */
import type { ChatMessage } from '../types';
export interface ChatUser {
    id: string;
    name: string;
    avatar_url?: string;
    isOnline: boolean;
}
export interface ChatMessageWithUser extends ChatMessage {
    user: ChatUser;
}
export interface TypingUser {
    id: string;
    name: string;
}
export declare class ChatService {
    private sessionId;
    private isEncryptionEnabled;
    private messages;
    private typingUsers;
    private typingTimeouts;
    private onMessageCallback?;
    private onMessagesLoadedCallback?;
    private onTypingChangedCallback?;
    private onErrorCallback?;
    constructor();
    /**
     * Initialize chat for a session
     */
    initializeSession(sessionId: string, enableEncryption?: boolean): Promise<void>;
    /**
     * Load existing messages from Supabase
     */
    private loadMessages;
    /**
     * Process a message (decrypt if needed and add user info)
     */
    private processMessage;
    /**
     * Subscribe to new messages
     */
    private subscribeToMessages;
    /**
     * Send a message
     */
    sendMessage(content: string): Promise<void>;
    /**
     * Send typing indicator
     */
    sendTypingStart(): void;
    /**
     * Send typing stop indicator
     */
    sendTypingStop(): void;
    /**
     * Handle typing start from another user
     */
    handleTypingStart(userId: string, userName: string): void;
    /**
     * Handle typing stop from another user
     */
    handleTypingStop(userId: string): void;
    /**
     * Notify about typing changes
     */
    private notifyTypingChanged;
    /**
     * Get all messages
     */
    getMessages(): ChatMessageWithUser[];
    /**
     * Get typing users
     */
    getTypingUsers(): TypingUser[];
    /**
     * Clear all messages (local only)
     */
    clearMessages(): void;
    /**
     * Disconnect from chat
     */
    disconnect(): void;
    /**
     * Set event callbacks
     */
    onMessage(callback: (message: ChatMessageWithUser) => void): void;
    onMessagesLoaded(callback: (messages: ChatMessageWithUser[]) => void): void;
    onTypingChanged(callback: (typingUsers: TypingUser[]) => void): void;
    onError(callback: (error: string) => void): void;
    /**
     * Format message for display
     */
    static formatMessage(message: ChatMessageWithUser): string;
    /**
     * Check if message contains code
     */
    static isCodeMessage(content: string): boolean;
    /**
     * Extract code blocks from message
     */
    static extractCodeBlocks(content: string): Array<{
        language: string;
        code: string;
    }>;
}
export declare const chatService: ChatService;
//# sourceMappingURL=chatService.d.ts.map