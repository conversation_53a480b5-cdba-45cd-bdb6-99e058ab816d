{"version": 3, "file": "jitsiService.js", "sourceRoot": "", "sources": ["../../services/jitsiService.ts"], "names": [], "mappings": "AAAA;;GAEG;AAoBH,MAAM,OAAO,YAAY;IACf,GAAG,GAAwB,IAAI,CAAC;IAChC,SAAS,GAAuB,IAAI,CAAC;IACrC,aAAa,GAAY,KAAK,CAAC;IAC/B,aAAa,GAAuB,IAAI,CAAC;IAEjD,kBAAkB;IACV,eAAe,CAAc;IAC7B,2BAA2B,CAA8B;IACzD,yBAAyB,CAA8B;IACvD,+BAA+B,CAA8B;IAC7D,6BAA6B,CAAc;IAEnD;QACE,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,eAAe;QAC3B,IAAI,MAAM,CAAC,oBAAoB,EAAE,CAAC;YAChC,OAAO;QACT,CAAC;QAED,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,GAAG,GAAG,qCAAqC,CAAC;YACnD,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;YACpB,MAAM,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC,OAAO,EAAE,CAAC;YAChC,MAAM,CAAC,OAAO,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,+BAA+B,CAAC,CAAC,CAAC;YAC1E,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,SAAsB,EAAE,MAAmB;QAC1D,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;QACvB,CAAC;QAED,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;QAE7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC;QAE5B,MAAM,OAAO,GAAG;YACd,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,KAAK,EAAE,MAAM;YACb,MAAM,EAAE,MAAM;YACd,UAAU,EAAE,SAAS;YACrB,GAAG,EAAE,MAAM,CAAC,GAAG;YACf,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,eAAe,EAAE;gBACf,mBAAmB,EAAE,KAAK;gBAC1B,mBAAmB,EAAE,KAAK;gBAC1B,iBAAiB,EAAE,KAAK;gBACxB,eAAe,EAAE,KAAK;gBACtB,kBAAkB,EAAE,KAAK;gBACzB,sBAAsB,EAAE,IAAI;gBAC5B,cAAc,EAAE,IAAI;gBACpB,oBAAoB;gBACpB,UAAU,EAAE,IAAI;gBAChB,UAAU,EAAE;oBACV,OAAO,EAAE,+BAA+B;oBACxC,OAAO,EAAE,uCAAuC;iBACjD;gBACD,mBAAmB;gBACnB,cAAc,EAAE;oBACd,YAAY;oBACZ,QAAQ;oBACR,gBAAgB;oBAChB,SAAS;oBACT,YAAY;oBACZ,mBAAmB;oBACnB,QAAQ;oBACR,SAAS;oBACT,MAAM;oBACN,WAAW;oBACX,eAAe;oBACf,UAAU;oBACV,aAAa;oBACb,UAAU;oBACV,WAAW;oBACX,cAAc;oBACd,WAAW;oBACX,QAAQ;oBACR,UAAU;oBACV,OAAO;oBACP,WAAW;oBACX,UAAU;oBACV,qBAAqB;oBACrB,UAAU;oBACV,MAAM;oBACN,eAAe;oBACf,UAAU;iBACX;aACF;YACD,wBAAwB,EAAE;gBACxB,oBAAoB,EAAE,KAAK;gBAC3B,yBAAyB,EAAE,KAAK;gBAChC,oBAAoB,EAAE,KAAK;gBAC3B,oBAAoB,EAAE,EAAE;gBACxB,eAAe,EAAE,KAAK;gBACtB,4BAA4B,EAAE,KAAK;gBACnC,+CAA+C,EAAE,KAAK;gBACtD,QAAQ,EAAE,OAAO;gBACjB,eAAe,EAAE,OAAO;gBACxB,aAAa,EAAE,OAAO;gBACtB,uCAAuC;gBACvC,eAAe,EAAE,IAAI;gBACrB,uBAAuB,EAAE,KAAK;gBAC9B,sBAAsB,EAAE,KAAK;gBAC7B,kBAAkB,EAAE,SAAS;gBAC7B,uBAAuB;gBACvB,gBAAgB,EAAE,KAAK;gBACvB,4BAA4B,EAAE,EAAE;gBAChC,wBAAwB,EAAE,EAAE;aAC7B;SACF,CAAC;QAEF,IAAI,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAEnE,yBAAyB;QACzB,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAE3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,kCAAkC,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC;IACnE,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO;QAEtB,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,uBAAuB,EAAE,CAAC,WAAgB,EAAE,EAAE;YACtE,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,WAAW,CAAC,CAAC;YACrD,IAAI,IAAI,CAAC,+BAA+B,EAAE,CAAC;gBACzC,IAAI,CAAC,+BAA+B,CAAC,WAAW,CAAC,CAAC;YACpD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,qBAAqB,EAAE,GAAG,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;YACrC,IAAI,IAAI,CAAC,6BAA6B,EAAE,CAAC;gBACvC,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACvC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,mBAAmB,EAAE,CAAC,WAAgB,EAAE,EAAE;YAClE,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;YAChD,IAAI,IAAI,CAAC,2BAA2B,EAAE,CAAC;gBACrC,IAAI,CAAC,2BAA2B,CAAC,WAAW,CAAC,CAAC;YAChD,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,iBAAiB,EAAE,CAAC,WAAgB,EAAE,EAAE;YAChE,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;YAC9C,IAAI,IAAI,CAAC,yBAAyB,EAAE,CAAC;gBACnC,IAAI,CAAC,yBAAyB,CAAC,WAAW,CAAC,CAAC;YAC9C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,cAAc,EAAE,GAAG,EAAE;YAC7C,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,IAAI,CAAC,OAAO,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,CAAC,KAAU,EAAE,EAAE;YACjE,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC,wBAAwB,EAAE,CAAC,KAAU,EAAE,EAAE;YACjE,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC;YACnB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;QAClB,CAAC;QAED,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,CAAC;QAChC,CAAC;QAED,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO;QAEtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QAC9C,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO;QAEtB,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;QAC9C,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;QACvC,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,CAAC,OAAO,CAAC,CAAC;IAC1C,CAAC;IAED;;OAEG;IACH,gBAAgB;QACd,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO;QACtB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,mBAAmB,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,MAAM;QACJ,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO;QACtB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAAY;QACzB,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO;QACtB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;IAC/C,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,OAAe;QAC7B,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO;QACtB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,iBAAiB,EAAE,OAAO,CAAC,CAAC;IACtD,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO,KAAK,CAAC;QAC5B,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO,KAAK,CAAC;QAC5B,OAAO,MAAM,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,CAAC,IAAI,CAAC,GAAG;YAAE,OAAO,SAAS,CAAC;QAChC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,QAAoB;QAC1B,IAAI,CAAC,eAAe,GAAG,QAAQ,CAAC;IAClC,CAAC;IAED,mBAAmB,CAAC,QAAoC;QACtD,IAAI,CAAC,2BAA2B,GAAG,QAAQ,CAAC;IAC9C,CAAC;IAED,iBAAiB,CAAC,QAAoC;QACpD,IAAI,CAAC,yBAAyB,GAAG,QAAQ,CAAC;IAC5C,CAAC;IAED,uBAAuB,CAAC,QAAoC;QAC1D,IAAI,CAAC,+BAA+B,GAAG,QAAQ,CAAC;IAClD,CAAC;IAED,qBAAqB,CAAC,QAAoB;QACxC,IAAI,CAAC,6BAA6B,GAAG,QAAQ,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,SAAiB;QACvC,gDAAgD;QAChD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;QAC1C,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;QACvD,OAAO,SAAS,SAAS,IAAI,SAAS,IAAI,MAAM,EAAE,CAAC;IACrD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,SAAiB,EAAE,QAAiD;QACtF,OAAO;YACL,MAAM,EAAE,aAAa;YACrB,QAAQ,EAAE,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC;YAC1C,QAAQ;SACT,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW;QAChB,2BAA2B;QAC3B,OAAO,CAAC,CAAC,CACP,SAAS,CAAC,YAAY;YACtB,OAAO,SAAS,CAAC,YAAY,CAAC,YAAY,KAAK,UAAU;YACzD,MAAM,CAAC,iBAAiB,CACzB,CAAC;IACJ,CAAC;CACF;AAED,MAAM,CAAC,MAAM,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC"}