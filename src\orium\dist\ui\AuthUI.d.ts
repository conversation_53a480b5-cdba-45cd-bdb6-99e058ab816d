/**
 * Authentication UI for Orium
 * Creates and manages authentication forms and modals
 */
export declare class AuthUI {
    private container;
    private currentView;
    private mfaData;
    constructor(container: HTMLElement);
    /**
     * Initialize the authentication UI
     */
    private initialize;
    /**
     * Handle authentication state changes
     */
    private handleAuthStateChange;
    /**
     * Render the authentication UI
     */
    private render;
    /**
     * Get HTML for current view
     */
    private getHTML;
    /**
     * Get sign-in form HTML
     */
    private getSignInHTML;
    /**
     * Get sign-up form HTML
     */
    private getSignUpHTML;
    /**
     * Get magic link form HTML
     */
    private getMagicLinkHTML;
    /**
     * Get MFA form HTML
     */
    private getMFAHTML;
    /**
     * Attach event listeners
     */
    private attachEventListeners;
    /**
     * Handle sign-in form submission
     */
    private handleSignIn;
    /**
     * Handle sign-up form submission
     */
    private handleSignUp;
    /**
     * Handle magic link form submission
     */
    private handleMagicLink;
    /**
     * Handle MFA form submission
     */
    private handleMFA;
    /**
     * Handle OAuth sign-in
     */
    private handleOAuth;
    /**
     * Update loading state
     */
    private updateLoadingState;
    /**
     * Update error state
     */
    private updateErrorState;
    /**
     * Show the authentication UI
     */
    show(): void;
    /**
     * Hide the authentication UI
     */
    hide(): void;
}
//# sourceMappingURL=AuthUI.d.ts.map