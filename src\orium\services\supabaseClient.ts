/**
 * Supabase client service for Orium
 */

import { createClient, SupabaseClient, User, Session } from '@supabase/supabase-js';
import { SUPABASE_CONFIG } from '../config/supabase';
import type { OriumUser, OriumSession, OriumDocument, ChatMessage, UserPresence, ApiKey } from '../types';

export class SupabaseService {
  private client: SupabaseClient;
  private currentUser: User | null = null;
  private currentSession: Session | null = null;

  constructor() {
    this.client = createClient(SUPABASE_CONFIG.url, SUPABASE_CONFIG.anonKey);
    this.initializeAuth();
  }

  private async initializeAuth() {
    const { data: { session } } = await this.client.auth.getSession();
    this.currentSession = session;
    this.currentUser = session?.user || null;

    // Listen for auth changes
    this.client.auth.onAuthStateChange((event, session) => {
      this.currentSession = session;
      this.currentUser = session?.user || null;
    });
  }

  // Authentication methods
  async signUp(email: string, password: string, metadata?: { name?: string }) {
    const { data, error } = await this.client.auth.signUp({
      email,
      password,
      options: {
        data: metadata
      }
    });
    return { data, error };
  }

  async signIn(email: string, password: string) {
    const { data, error } = await this.client.auth.signInWithPassword({
      email,
      password
    });
    return { data, error };
  }

  async signInWithOAuth(provider: 'google' | 'github') {
    const { data, error } = await this.client.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${window.location.origin}/auth/callback`
      }
    });
    return { data, error };
  }

  async signInWithOtp(email: string) {
    const { data, error } = await this.client.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${window.location.origin}/auth/callback`
      }
    });
    return { data, error };
  }

  async signOut() {
    const { error } = await this.client.auth.signOut();
    return { error };
  }

  async enableMFA() {
    const { data, error } = await this.client.auth.mfa.enroll({
      factorType: 'totp'
    });
    return { data, error };
  }

  async verifyMFA(factorId: string, challengeId: string, code: string) {
    const { data, error } = await this.client.auth.mfa.verify({
      factorId,
      challengeId,
      code
    });
    return { data, error };
  }

  // User methods
  getCurrentUser(): User | null {
    return this.currentUser;
  }

  getCurrentSession(): Session | null {
    return this.currentSession;
  }

  // Session methods
  async createSession(name: string, mode: 'collaborative' | 'vibe'): Promise<{ data: OriumSession | null; error: any }> {
    if (!this.currentUser) {
      return { data: null, error: new Error('User not authenticated') };
    }

    const { data, error } = await this.client
      .from('sessions')
      .insert({
        name,
        owner_id: this.currentUser.id,
        mode,
        is_active: true
      })
      .select()
      .single();

    return { data, error };
  }

  async getSession(sessionId: string): Promise<{ data: OriumSession | null; error: any }> {
    const { data, error } = await this.client
      .from('sessions')
      .select('*')
      .eq('id', sessionId)
      .single();

    return { data, error };
  }

  async getUserSessions(): Promise<{ data: OriumSession[] | null; error: any }> {
    if (!this.currentUser) {
      return { data: null, error: new Error('User not authenticated') };
    }

    const { data, error } = await this.client
      .from('sessions')
      .select('*')
      .eq('owner_id', this.currentUser.id)
      .order('created_at', { ascending: false });

    return { data, error };
  }

  // Document methods
  async createDocument(sessionId: string, name: string, content: string, language: string): Promise<{ data: OriumDocument | null; error: any }> {
    const { data, error } = await this.client
      .from('documents')
      .insert({
        session_id: sessionId,
        name,
        content,
        language
      })
      .select()
      .single();

    return { data, error };
  }

  async getSessionDocuments(sessionId: string): Promise<{ data: OriumDocument[] | null; error: any }> {
    const { data, error } = await this.client
      .from('documents')
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: false });

    return { data, error };
  }

  // Chat methods
  async sendChatMessage(sessionId: string, content: string, isEncrypted: boolean = false): Promise<{ data: ChatMessage | null; error: any }> {
    if (!this.currentUser) {
      return { data: null, error: new Error('User not authenticated') };
    }

    const { data, error } = await this.client
      .from('chat_messages')
      .insert({
        session_id: sessionId,
        user_id: this.currentUser.id,
        content,
        is_encrypted: isEncrypted
      })
      .select()
      .single();

    return { data, error };
  }

  async getChatMessages(sessionId: string): Promise<{ data: ChatMessage[] | null; error: any }> {
    const { data, error } = await this.client
      .from('chat_messages')
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at', { ascending: true });

    return { data, error };
  }

  // Presence methods
  async updatePresence(sessionId: string, presence: Partial<UserPresence>): Promise<{ error: any }> {
    if (!this.currentUser) {
      return { error: new Error('User not authenticated') };
    }

    const { error } = await this.client
      .from('user_presence')
      .upsert({
        user_id: this.currentUser.id,
        session_id: sessionId,
        ...presence,
        last_seen: new Date().toISOString()
      });

    return { error };
  }

  async getSessionPresence(sessionId: string): Promise<{ data: UserPresence[] | null; error: any }> {
    const { data, error } = await this.client
      .from('user_presence')
      .select('*')
      .eq('session_id', sessionId)
      .gte('last_seen', new Date(Date.now() - 30000).toISOString()); // Active in last 30 seconds

    return { data, error };
  }

  // Real-time subscriptions
  subscribeToSession(sessionId: string, callback: (payload: any) => void) {
    return this.client
      .channel(`session:${sessionId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'documents',
        filter: `session_id=eq.${sessionId}`
      }, callback)
      .subscribe();
  }

  subscribeToChat(sessionId: string, callback: (payload: any) => void) {
    return this.client
      .channel(`chat:${sessionId}`)
      .on('postgres_changes', {
        event: 'INSERT',
        schema: 'public',
        table: 'chat_messages',
        filter: `session_id=eq.${sessionId}`
      }, callback)
      .subscribe();
  }

  subscribeToPresence(sessionId: string, callback: (payload: any) => void) {
    return this.client
      .channel(`presence:${sessionId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'user_presence',
        filter: `session_id=eq.${sessionId}`
      }, callback)
      .subscribe();
  }

  // API Key management
  async saveApiKey(provider: string, encryptedKey: string): Promise<{ data: ApiKey | null; error: any }> {
    if (!this.currentUser) {
      return { data: null, error: new Error('User not authenticated') };
    }

    const { data, error } = await this.client
      .from('api_keys')
      .upsert({
        user_id: this.currentUser.id,
        provider,
        encrypted_key: encryptedKey
      })
      .select()
      .single();

    return { data, error };
  }

  async getApiKeys(): Promise<{ data: ApiKey[] | null; error: any }> {
    if (!this.currentUser) {
      return { data: null, error: new Error('User not authenticated') };
    }

    const { data, error } = await this.client
      .from('api_keys')
      .select('*')
      .eq('user_id', this.currentUser.id);

    return { data, error };
  }
}

export const supabaseService = new SupabaseService();
