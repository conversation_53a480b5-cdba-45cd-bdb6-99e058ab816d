/**
 * WebSocket server for real-time collaboration using Yjs
 * This server handles document synchronization and user presence
 */
import { WebSocket, WebSocketServer } from 'ws';
// import { setupWSConnection, docs } from 'y-websocket/bin/utils';
import * as http from 'http';
import * as url from 'url';
export class OriumWebSocketServer {
    wss = null;
    server = null;
    port = 1234;
    rooms = new Map();
    userSessions = new Map();
    /**
     * Start the WebSocket server
     */
    async start(port = 1234) {
        this.port = port;
        // Create HTTP server
        this.server = http.createServer();
        // Create WebSocket server
        this.wss = new WebSocketServer({
            server: this.server,
            path: '/collaboration'
        });
        // Handle WebSocket connections
        this.wss.on('connection', this.handleConnection.bind(this));
        // Start the server
        return new Promise((resolve, reject) => {
            this.server.listen(port, (err) => {
                if (err) {
                    reject(err);
                }
                else {
                    console.log(`Orium WebSocket server started on port ${port}`);
                    resolve();
                }
            });
        });
    }
    /**
     * Stop the WebSocket server
     */
    async stop() {
        return new Promise((resolve) => {
            if (this.wss) {
                this.wss.close(() => {
                    console.log('WebSocket server closed');
                });
            }
            if (this.server) {
                this.server.close(() => {
                    console.log('HTTP server closed');
                    resolve();
                });
            }
            else {
                resolve();
            }
        });
    }
    /**
     * Handle new WebSocket connection
     */
    handleConnection(ws, req) {
        const urlParts = url.parse(req.url || '', true);
        const roomName = urlParts.query.room;
        const userId = urlParts.query.userId;
        const userName = urlParts.query.userName;
        if (!roomName || !userId) {
            ws.close(1008, 'Room name and user ID are required');
            return;
        }
        console.log(`User ${userName || userId} connected to room ${roomName}`);
        // Store user session info
        this.userSessions.set(ws, { userId, roomName, userName: userName || userId });
        // Add to room
        if (!this.rooms.has(roomName)) {
            this.rooms.set(roomName, new Set());
        }
        this.rooms.get(roomName).add(ws);
        // Set up Yjs connection (placeholder - would use setupWSConnection in production)
        // setupWSConnection(ws, req, { docName: roomName });
        // Handle connection close
        ws.on('close', () => {
            this.handleDisconnection(ws);
        });
        // Handle errors
        ws.on('error', (error) => {
            console.error('WebSocket error:', error);
            this.handleDisconnection(ws);
        });
        // Send welcome message
        this.sendToClient(ws, {
            type: 'welcome',
            roomName,
            connectedUsers: this.getConnectedUsers(roomName)
        });
        // Notify other users in the room
        this.broadcastToRoom(roomName, {
            type: 'user-joined',
            userId,
            userName: userName || userId,
            connectedUsers: this.getConnectedUsers(roomName)
        }, ws);
    }
    /**
     * Handle WebSocket disconnection
     */
    handleDisconnection(ws) {
        const session = this.userSessions.get(ws);
        if (!session)
            return;
        const { userId, roomName, userName } = session;
        console.log(`User ${userName} disconnected from room ${roomName}`);
        // Remove from room
        const room = this.rooms.get(roomName);
        if (room) {
            room.delete(ws);
            if (room.size === 0) {
                this.rooms.delete(roomName);
                // Clean up Yjs document if no one is connected (placeholder)
                // if (docs.has(roomName)) {
                //   const doc = docs.get(roomName);
                //   if (doc) {
                //     doc.destroy();
                //     docs.delete(roomName);
                //   }
                // }
            }
        }
        // Remove user session
        this.userSessions.delete(ws);
        // Notify other users in the room
        if (room && room.size > 0) {
            this.broadcastToRoom(roomName, {
                type: 'user-left',
                userId,
                userName,
                connectedUsers: this.getConnectedUsers(roomName)
            });
        }
    }
    /**
     * Send message to a specific client
     */
    sendToClient(ws, message) {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        }
    }
    /**
     * Broadcast message to all users in a room
     */
    broadcastToRoom(roomName, message, excludeWs) {
        const room = this.rooms.get(roomName);
        if (!room)
            return;
        const messageStr = JSON.stringify(message);
        room.forEach((ws) => {
            if (ws !== excludeWs && ws.readyState === WebSocket.OPEN) {
                ws.send(messageStr);
            }
        });
    }
    /**
     * Get number of connected users in a room
     */
    getConnectedUsers(roomName) {
        const room = this.rooms.get(roomName);
        return room ? room.size : 0;
    }
    /**
     * Get list of all active rooms
     */
    getRooms() {
        return Array.from(this.rooms.keys());
    }
    /**
     * Get user info for a room
     */
    getRoomUsers(roomName) {
        const room = this.rooms.get(roomName);
        if (!room)
            return [];
        const users = [];
        room.forEach((ws) => {
            const session = this.userSessions.get(ws);
            if (session) {
                users.push({
                    userId: session.userId,
                    userName: session.userName
                });
            }
        });
        return users;
    }
    /**
     * Send message to specific user
     */
    sendToUser(roomName, userId, message) {
        const room = this.rooms.get(roomName);
        if (!room)
            return false;
        for (const ws of room) {
            const session = this.userSessions.get(ws);
            if (session && session.userId === userId) {
                this.sendToClient(ws, message);
                return true;
            }
        }
        return false;
    }
    /**
     * Broadcast presence update
     */
    broadcastPresence(roomName, userId, presence) {
        this.broadcastToRoom(roomName, {
            type: 'presence-update',
            userId,
            presence
        });
    }
    /**
     * Handle custom messages (for chat, presence, etc.)
     */
    handleCustomMessage(ws, message) {
        const session = this.userSessions.get(ws);
        if (!session)
            return;
        const { roomName, userId } = session;
        switch (message.type) {
            case 'chat-message':
                this.broadcastToRoom(roomName, {
                    type: 'chat-message',
                    userId,
                    userName: session.userName,
                    content: message.content,
                    timestamp: Date.now()
                });
                break;
            case 'cursor-update':
                this.broadcastToRoom(roomName, {
                    type: 'cursor-update',
                    userId,
                    cursor: message.cursor
                }, ws);
                break;
            case 'selection-update':
                this.broadcastToRoom(roomName, {
                    type: 'selection-update',
                    userId,
                    selection: message.selection
                }, ws);
                break;
            case 'typing-start':
                this.broadcastToRoom(roomName, {
                    type: 'typing-start',
                    userId,
                    userName: session.userName
                }, ws);
                break;
            case 'typing-stop':
                this.broadcastToRoom(roomName, {
                    type: 'typing-stop',
                    userId
                }, ws);
                break;
            default:
                console.log('Unknown message type:', message.type);
        }
    }
    /**
     * Get server statistics
     */
    getStats() {
        const rooms = Array.from(this.rooms.entries()).map(([name, users]) => ({
            name,
            users: users.size
        }));
        return {
            totalRooms: this.rooms.size,
            totalUsers: Array.from(this.userSessions.values()).length,
            rooms
        };
    }
}
// Create and export server instance
export const collaborationServer = new OriumWebSocketServer();
// Start server if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const port = process.env.PORT ? parseInt(process.env.PORT) : 1234;
    collaborationServer.start(port).catch(console.error);
    // Graceful shutdown
    process.on('SIGINT', async () => {
        console.log('Shutting down server...');
        await collaborationServer.stop();
        process.exit(0);
    });
    process.on('SIGTERM', async () => {
        console.log('Shutting down server...');
        await collaborationServer.stop();
        process.exit(0);
    });
}
//# sourceMappingURL=websocketServer.js.map