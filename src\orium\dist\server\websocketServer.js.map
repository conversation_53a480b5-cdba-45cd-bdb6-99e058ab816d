{"version": 3, "file": "websocketServer.js", "sourceRoot": "", "sources": ["../../server/websocketServer.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAGH,OAAO,EAAE,SAAS,EAAE,eAAe,EAAE,MAAM,IAAI,CAAC;AAChD,mEAAmE;AACnE,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,KAAK,GAAG,MAAM,KAAK,CAAC;AAS3B,MAAM,OAAO,oBAAoB;IACvB,GAAG,GAA2B,IAAI,CAAC;IACnC,MAAM,GAAuB,IAAI,CAAC;IAClC,IAAI,GAAW,IAAI,CAAC;IACpB,KAAK,GAAgC,IAAI,GAAG,EAAE,CAAC;IAC/C,YAAY,GAA2E,IAAI,GAAG,EAAE,CAAC;IAEzG;;OAEG;IACH,KAAK,CAAC,KAAK,CAAC,OAAe,IAAI;QAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QAEjB,qBAAqB;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAElC,0BAA0B;QAC1B,IAAI,CAAC,GAAG,GAAG,IAAI,eAAe,CAAC;YAC7B,MAAM,EAAE,IAAI,CAAC,MAAM;YACnB,IAAI,EAAE,gBAAgB;SACvB,CAAC,CAAC;QAEH,+BAA+B;QAC/B,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QAE5D,mBAAmB;QACnB,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC,MAAO,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,GAAW,EAAE,EAAE;gBACxC,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,GAAG,CAAC,CAAC;gBACd,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,GAAG,CAAC,0CAA0C,IAAI,EAAE,CAAC,CAAC;oBAC9D,OAAO,EAAE,CAAC;gBACZ,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;gBACb,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE;oBAClB,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;gBACzC,CAAC,CAAC,CAAC;YACL,CAAC;YAED,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;oBACrB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;oBAClC,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,gBAAgB,CAAC,EAAa,EAAE,GAAyB;QAC/D,MAAM,QAAQ,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,EAAE,EAAE,IAAI,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAc,CAAC;QAC/C,MAAM,MAAM,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAgB,CAAC;QAC/C,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,QAAkB,CAAC;QAEnD,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,EAAE,CAAC;YACzB,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE,oCAAoC,CAAC,CAAC;YACrD,OAAO;QACT,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ,IAAI,MAAM,sBAAsB,QAAQ,EAAE,CAAC,CAAC;QAExE,0BAA0B;QAC1B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,IAAI,MAAM,EAAE,CAAC,CAAC;QAE9E,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;QACtC,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAElC,kFAAkF;QAClF,qDAAqD;QAErD,0BAA0B;QAC1B,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YAClB,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;YACvB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC;QAEH,uBAAuB;QACvB,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE;YACpB,IAAI,EAAE,SAAS;YACf,QAAQ;YACR,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;SACjD,CAAC,CAAC;QAEH,iCAAiC;QACjC,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;YAC7B,IAAI,EAAE,aAAa;YACnB,MAAM;YACN,QAAQ,EAAE,QAAQ,IAAI,MAAM;YAC5B,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;SACjD,EAAE,EAAE,CAAC,CAAC;IACT,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,EAAa;QACvC,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,CAAC;QAE/C,OAAO,CAAC,GAAG,CAAC,QAAQ,QAAQ,2BAA2B,QAAQ,EAAE,CAAC,CAAC;QAEnE,mBAAmB;QACnB,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,IAAI,EAAE,CAAC;YACT,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YAChB,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBACpB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBAC5B,6DAA6D;gBAC7D,4BAA4B;gBAC5B,oCAAoC;gBACpC,eAAe;gBACf,qBAAqB;gBACrB,6BAA6B;gBAC7B,MAAM;gBACN,IAAI;YACN,CAAC;QACH,CAAC;QAED,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;QAE7B,iCAAiC;QACjC,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;gBAC7B,IAAI,EAAE,WAAW;gBACjB,MAAM;gBACN,QAAQ;gBACR,cAAc,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC;aACjD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,YAAY,CAAC,EAAa,EAAE,OAAY;QAC9C,IAAI,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;YACrC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;QACnC,CAAC;IACH,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAgB,EAAE,OAAY,EAAE,SAAqB;QAC3E,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI;YAAE,OAAO;QAElB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAC3C,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAClB,IAAI,EAAE,KAAK,SAAS,IAAI,EAAE,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;gBACzD,EAAE,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YACtB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB;QAChC,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,QAAQ;QACN,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,QAAgB;QAC3B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI;YAAE,OAAO,EAAE,CAAC;QAErB,MAAM,KAAK,GAAgD,EAAE,CAAC;QAC9D,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,EAAE,EAAE;YAClB,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1C,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,CAAC,IAAI,CAAC;oBACT,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,QAAQ,EAAE,OAAO,CAAC,QAAQ;iBAC3B,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,QAAgB,EAAE,MAAc,EAAE,OAAY;QACvD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QACtC,IAAI,CAAC,IAAI;YAAE,OAAO,KAAK,CAAC;QAExB,KAAK,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC;YACtB,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;YAC1C,IAAI,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC;gBACzC,IAAI,CAAC,YAAY,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;gBAC/B,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,QAAgB,EAAE,MAAc,EAAE,QAAa;QAC/D,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;YAC7B,IAAI,EAAE,iBAAiB;YACvB,MAAM;YACN,QAAQ;SACT,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACK,mBAAmB,CAAC,EAAa,EAAE,OAAY;QACrD,MAAM,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC;QAErC,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,cAAc;gBACjB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;oBAC7B,IAAI,EAAE,cAAc;oBACpB,MAAM;oBACN,QAAQ,EAAE,OAAO,CAAC,QAAQ;oBAC1B,OAAO,EAAE,OAAO,CAAC,OAAO;oBACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iBACtB,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,eAAe;gBAClB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;oBAC7B,IAAI,EAAE,eAAe;oBACrB,MAAM;oBACN,MAAM,EAAE,OAAO,CAAC,MAAM;iBACvB,EAAE,EAAE,CAAC,CAAC;gBACP,MAAM;YAER,KAAK,kBAAkB;gBACrB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;oBAC7B,IAAI,EAAE,kBAAkB;oBACxB,MAAM;oBACN,SAAS,EAAE,OAAO,CAAC,SAAS;iBAC7B,EAAE,EAAE,CAAC,CAAC;gBACP,MAAM;YAER,KAAK,cAAc;gBACjB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;oBAC7B,IAAI,EAAE,cAAc;oBACpB,MAAM;oBACN,QAAQ,EAAE,OAAO,CAAC,QAAQ;iBAC3B,EAAE,EAAE,CAAC,CAAC;gBACP,MAAM;YAER,KAAK,aAAa;gBAChB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;oBAC7B,IAAI,EAAE,aAAa;oBACnB,MAAM;iBACP,EAAE,EAAE,CAAC,CAAC;gBACP,MAAM;YAER;gBACE,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC;IACH,CAAC;IAED;;OAEG;IACH,QAAQ;QAKN,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,CAAC;YACrE,IAAI;YACJ,KAAK,EAAE,KAAK,CAAC,IAAI;SAClB,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI;YAC3B,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM;YACzD,KAAK;SACN,CAAC;IACJ,CAAC;CACF;AAED,oCAAoC;AACpC,MAAM,CAAC,MAAM,mBAAmB,GAAG,IAAI,oBAAoB,EAAE,CAAC;AAE9D,4CAA4C;AAC5C,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,UAAU,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IACpD,MAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAClE,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IAErD,oBAAoB;IACpB,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,KAAK,IAAI,EAAE;QAC9B,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,MAAM,mBAAmB,CAAC,IAAI,EAAE,CAAC;QACjC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;IAEH,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,KAAK,IAAI,EAAE;QAC/B,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC,CAAC;QACvC,MAAM,mBAAmB,CAAC,IAAI,EAAE,CAAC;QACjC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACL,CAAC"}