/**
 * Collaborative Coding Mode Component
 * Manages the collaborative editing interface with live cursors, presence, and real-time sync
 */
import { CollaborationUser } from '../services/collaborationService';
export interface CollaborativeModeConfig {
    sessionId: string;
    enableVideo: boolean;
    enableChat: boolean;
    showCursors: boolean;
    websocketUrl?: string;
}
export declare class CollaborativeCodingMode {
    private config;
    private editor;
    private collaboration;
    private session;
    private document;
    private isInitialized;
    private container;
    private editorContainer;
    private sidebarContainer;
    private videoContainer;
    private chatContainer;
    private presenceContainer;
    private connectedUsers;
    private chatMessages;
    private typingUsers;
    private onUserJoinedCallback?;
    private onUserLeftCallback?;
    private onDocumentChangedCallback?;
    constructor(container: HTMLElement, config: CollaborativeModeConfig);
    /**
     * Initialize the collaborative coding mode
     */
    initialize(): Promise<void>;
    /**
     * Load session data from Supabase
     */
    private loadSession;
    /**
     * Create the UI layout
     */
    private createUI;
    /**
     * Apply CSS styles
     */
    private applyStyles;
    /**
     * Initialize Monaco editor
     */
    private initializeEditor;
    /**
     * Initialize collaboration
     */
    private initializeCollaboration;
    /**
     * Initialize chat
     */
    private initializeChat;
    /**
     * Initialize video conferencing
     */
    private initializeVideo;
    /**
     * Update presence indicators
     */
    private updatePresenceIndicators;
    /**
     * Update user cursor position
     */
    private updateUserCursor;
    /**
     * Update user selection
     */
    private updateUserSelection;
    /**
     * Send chat message
     */
    private sendChatMessage;
    /**
     * Add chat message to UI
     */
    private addChatMessage;
    /**
     * Render all chat messages
     */
    private renderChatMessages;
    /**
     * Render single chat message
     */
    private renderChatMessage;
    /**
     * Get chat message HTML
     */
    private getChatMessageHTML;
    /**
     * Format message content (handle code blocks, etc.)
     */
    private formatMessageContent;
    /**
     * Update typing indicator
     */
    private updateTypingIndicator;
    /**
     * Scroll chat to bottom
     */
    private scrollChatToBottom;
    /**
     * Set up event listeners
     */
    private setupEventListeners;
    /**
     * Save document
     */
    private saveDocument;
    /**
     * Get current document content
     */
    getDocumentContent(): string;
    /**
     * Set document content
     */
    setDocumentContent(content: string): void;
    /**
     * Get connected users
     */
    getConnectedUsers(): CollaborationUser[];
    /**
     * Dispose of the collaborative mode
     */
    dispose(): void;
    /**
     * Set event callbacks
     */
    onUserJoined(callback: (user: CollaborationUser) => void): void;
    onUserLeft(callback: (userId: string) => void): void;
    onDocumentChanged(callback: (content: string) => void): void;
}
//# sourceMappingURL=CollaborativeCodingMode.d.ts.map