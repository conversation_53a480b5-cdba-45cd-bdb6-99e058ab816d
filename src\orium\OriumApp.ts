/**
 * Main Orium Application Class
 * Orchestrates the entire collaborative code editor experience
 */

import { authComponent, AuthState } from './components/AuthComponent';
import { AuthUI } from './ui/AuthUI';
import { CollaborativeCodingMode } from './components/CollaborativeCodingMode';
import { supabaseService } from './services/supabaseClient';
import { aiService } from './services/aiService';
import type { OriumMode, OriumSession } from './types';

export interface OriumConfig {
  websocketUrl?: string;
  enableAnalytics?: boolean;
  theme?: 'dark' | 'light';
}

export class OriumApp {
  private container: HTMLElement;
  private config: OriumConfig;
  private authUI: AuthUI | null = null;
  private currentMode: CollaborativeCodingMode | null = null;
  private currentSession: OriumSession | null = null;
  private isInitialized: boolean = false;

  constructor(container: HTMLElement, config: OriumConfig = {}) {
    this.container = container;
    this.config = {
      websocketUrl: 'ws://localhost:1234',
      enableAnalytics: false,
      theme: 'dark',
      ...config
    };

    this.initialize();
  }

  /**
   * Initialize the Orium application
   */
  private async initialize(): Promise<void> {
    try {
      console.log('Initializing Orium...');

      // Apply theme
      this.applyTheme();

      // Set up authentication
      this.setupAuthentication();

      // Check if user is already authenticated
      const currentUser = authComponent.getCurrentUser();
      if (currentUser) {
        await this.showMainInterface();
      } else {
        this.showAuthInterface();
      }

      this.isInitialized = true;
      console.log('Orium initialized successfully');
    } catch (error) {
      console.error('Failed to initialize Orium:', error);
      this.showError('Failed to initialize application');
    }
  }

  /**
   * Apply theme to the application
   */
  private applyTheme(): void {
    document.body.classList.add(`orium-theme-${this.config.theme}`);
    
    const style = document.createElement('style');
    style.textContent = `
      .orium-theme-dark {
        background: #1e1e1e;
        color: #ffffff;
      }
      
      .orium-theme-light {
        background: #ffffff;
        color: #000000;
      }
      
      .orium-app {
        width: 100%;
        height: 100vh;
        overflow: hidden;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      }
      
      .loading-screen {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: #1e1e1e;
        color: #ffffff;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #404040;
        border-top: 4px solid #007ACC;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .error-screen {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100vh;
        background: #1e1e1e;
        color: #ffffff;
        text-align: center;
        padding: 2rem;
      }
      
      .error-title {
        font-size: 2rem;
        margin-bottom: 1rem;
        color: #ff4444;
      }
      
      .error-message {
        font-size: 1.1rem;
        margin-bottom: 2rem;
        color: #cccccc;
      }
      
      .main-interface {
        height: 100vh;
        display: flex;
        flex-direction: column;
      }
      
      .app-header {
        background: #2d2d2d;
        border-bottom: 1px solid #404040;
        padding: 0.5rem 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      .app-logo {
        font-size: 1.5rem;
        font-weight: 600;
        color: #007ACC;
      }
      
      .app-controls {
        display: flex;
        gap: 1rem;
        align-items: center;
      }
      
      .user-info {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #ffffff;
      }
      
      .user-avatar {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background: #007ACC;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
      }
      
      .mode-selector {
        display: flex;
        gap: 0.5rem;
      }
      
      .mode-btn {
        padding: 0.5rem 1rem;
        border: 1px solid #404040;
        background: #2d2d2d;
        color: #ffffff;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .mode-btn:hover {
        background: #404040;
      }
      
      .mode-btn.active {
        background: #007ACC;
        border-color: #007ACC;
      }
      
      .app-content {
        flex: 1;
        overflow: hidden;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Set up authentication
   */
  private setupAuthentication(): void {
    authComponent.onStateChange((state: AuthState) => {
      if (state.user && !state.needsMFA) {
        // User authenticated successfully
        this.showMainInterface();
      } else if (!state.user) {
        // User not authenticated
        this.showAuthInterface();
      }
      // MFA and loading states are handled by AuthUI
    });
  }

  /**
   * Show authentication interface
   */
  private showAuthInterface(): void {
    this.container.innerHTML = '<div id="auth-container"></div>';
    const authContainer = document.getElementById('auth-container')!;
    
    this.authUI = new AuthUI(authContainer);
    this.authUI.show();
  }

  /**
   * Show main application interface
   */
  private async showMainInterface(): Promise<void> {
    this.showLoading('Loading workspace...');

    try {
      // Initialize AI service with user password (would be prompted in real app)
      // For demo purposes, we'll skip this step
      
      // Create main interface
      this.createMainInterface();

      // Load user sessions
      await this.loadUserSessions();

    } catch (error) {
      console.error('Failed to load main interface:', error);
      this.showError('Failed to load workspace');
    }
  }

  /**
   * Create main interface
   */
  private createMainInterface(): void {
    const user = authComponent.getCurrentUser();
    if (!user) return;

    this.container.innerHTML = `
      <div class="orium-app">
        <div class="main-interface">
          <div class="app-header">
            <div class="app-logo">Orium</div>
            <div class="app-controls">
              <div class="mode-selector">
                <button class="mode-btn active" data-mode="collaborative">Collaborative</button>
                <button class="mode-btn" data-mode="vibe">Vibe Coding</button>
              </div>
              <div class="user-info">
                <div class="user-avatar">
                  ${user.name ? user.name.charAt(0).toUpperCase() : user.email.charAt(0).toUpperCase()}
                </div>
                <span>${user.name || user.email}</span>
                <button id="logout-btn" class="mode-btn">Logout</button>
              </div>
            </div>
          </div>
          <div class="app-content" id="app-content">
            <div class="session-selector">
              <h2>Select or Create a Session</h2>
              <div class="session-actions">
                <button id="create-session-btn" class="mode-btn">Create New Session</button>
                <button id="join-session-btn" class="mode-btn">Join Session</button>
              </div>
              <div id="sessions-list"></div>
            </div>
          </div>
        </div>
      </div>
    `;

    this.attachMainInterfaceListeners();
  }

  /**
   * Attach event listeners for main interface
   */
  private attachMainInterfaceListeners(): void {
    // Logout button
    const logoutBtn = document.getElementById('logout-btn');
    if (logoutBtn) {
      logoutBtn.addEventListener('click', () => {
        authComponent.signOut();
      });
    }

    // Mode selector buttons
    const modeButtons = document.querySelectorAll('.mode-btn[data-mode]');
    modeButtons.forEach(btn => {
      btn.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        const mode = target.dataset.mode;
        
        // Update active state
        modeButtons.forEach(b => b.classList.remove('active'));
        target.classList.add('active');
        
        console.log(`Switched to ${mode} mode`);
      });
    });

    // Session actions
    const createSessionBtn = document.getElementById('create-session-btn');
    if (createSessionBtn) {
      createSessionBtn.addEventListener('click', () => {
        this.createNewSession();
      });
    }

    const joinSessionBtn = document.getElementById('join-session-btn');
    if (joinSessionBtn) {
      joinSessionBtn.addEventListener('click', () => {
        this.showJoinSessionDialog();
      });
    }
  }

  /**
   * Load user sessions
   */
  private async loadUserSessions(): Promise<void> {
    try {
      const { data: sessions, error } = await supabaseService.getUserSessions();
      
      if (error) {
        console.error('Failed to load sessions:', error);
        return;
      }

      this.renderSessionsList(sessions || []);
    } catch (error) {
      console.error('Error loading sessions:', error);
    }
  }

  /**
   * Render sessions list
   */
  private renderSessionsList(sessions: OriumSession[]): void {
    const sessionsList = document.getElementById('sessions-list');
    if (!sessionsList) return;

    if (sessions.length === 0) {
      sessionsList.innerHTML = '<p>No sessions found. Create a new session to get started.</p>';
      return;
    }

    sessionsList.innerHTML = `
      <h3>Your Sessions</h3>
      <div class="sessions-grid">
        ${sessions.map(session => `
          <div class="session-card" data-session-id="${session.id}">
            <h4>${session.name}</h4>
            <p>Mode: ${session.mode}</p>
            <p>Created: ${new Date(session.created_at).toLocaleDateString()}</p>
            <div class="session-actions">
              <button class="mode-btn" onclick="oriumApp.joinSession('${session.id}')">Join</button>
              <button class="mode-btn" onclick="oriumApp.deleteSession('${session.id}')">Delete</button>
            </div>
          </div>
        `).join('')}
      </div>
    `;

    // Add session card styles
    const style = document.createElement('style');
    style.textContent = `
      .sessions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1rem;
        margin-top: 1rem;
      }
      
      .session-card {
        background: #2d2d2d;
        border: 1px solid #404040;
        border-radius: 8px;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.2s;
      }
      
      .session-card:hover {
        border-color: #007ACC;
        background: #353535;
      }
      
      .session-card h4 {
        margin: 0 0 0.5rem 0;
        color: #ffffff;
      }
      
      .session-card p {
        margin: 0.25rem 0;
        color: #cccccc;
        font-size: 0.9rem;
      }
      
      .session-actions {
        margin-top: 1rem;
        display: flex;
        gap: 0.5rem;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Create new session
   */
  private async createNewSession(): Promise<void> {
    const sessionName = prompt('Enter session name:');
    if (!sessionName) return;

    try {
      const { data: session, error } = await supabaseService.createSession(sessionName, 'collaborative');
      
      if (error || !session) {
        this.showError('Failed to create session');
        return;
      }

      await this.joinSession(session.id);
    } catch (error) {
      console.error('Failed to create session:', error);
      this.showError('Failed to create session');
    }
  }

  /**
   * Join session
   */
  async joinSession(sessionId: string): Promise<void> {
    this.showLoading('Joining session...');

    try {
      // Load session data
      const { data: session, error } = await supabaseService.getSession(sessionId);
      
      if (error || !session) {
        this.showError('Session not found');
        return;
      }

      this.currentSession = session;

      // Initialize collaborative mode
      const appContent = document.getElementById('app-content');
      if (!appContent) return;

      // Dispose current mode if exists
      if (this.currentMode) {
        this.currentMode.dispose();
      }

      // Create new collaborative mode
      this.currentMode = new CollaborativeCodingMode(appContent, {
        sessionId: session.id,
        enableVideo: true,
        enableChat: true,
        showCursors: true,
        websocketUrl: this.config.websocketUrl
      });

      await this.currentMode.initialize();

      console.log(`Joined session: ${session.name}`);
    } catch (error) {
      console.error('Failed to join session:', error);
      this.showError('Failed to join session');
    }
  }

  /**
   * Show join session dialog
   */
  private showJoinSessionDialog(): void {
    const sessionId = prompt('Enter session ID:');
    if (sessionId) {
      this.joinSession(sessionId);
    }
  }

  /**
   * Delete session
   */
  async deleteSession(sessionId: string): Promise<void> {
    if (!confirm('Are you sure you want to delete this session?')) {
      return;
    }

    try {
      // This would be implemented with Supabase delete
      console.log(`Deleting session: ${sessionId}`);
      await this.loadUserSessions(); // Refresh list
    } catch (error) {
      console.error('Failed to delete session:', error);
      this.showError('Failed to delete session');
    }
  }

  /**
   * Show loading screen
   */
  private showLoading(message: string = 'Loading...'): void {
    this.container.innerHTML = `
      <div class="loading-screen">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p>${message}</p>
        </div>
      </div>
    `;
  }

  /**
   * Show error screen
   */
  private showError(message: string): void {
    this.container.innerHTML = `
      <div class="error-screen">
        <h1 class="error-title">Oops!</h1>
        <p class="error-message">${message}</p>
        <button class="mode-btn" onclick="location.reload()">Reload</button>
      </div>
    `;
  }

  /**
   * Get current session
   */
  getCurrentSession(): OriumSession | null {
    return this.currentSession;
  }

  /**
   * Get current mode
   */
  getCurrentMode(): CollaborativeCodingMode | null {
    return this.currentMode;
  }

  /**
   * Dispose of the application
   */
  dispose(): void {
    if (this.currentMode) {
      this.currentMode.dispose();
    }

    if (this.authUI) {
      this.authUI.hide();
    }

    this.container.innerHTML = '';
    console.log('Orium application disposed');
  }
}

// Global instance for easy access
declare global {
  interface Window {
    oriumApp: OriumApp;
  }
}

// Export for use in HTML
(window as any).oriumApp = null;
