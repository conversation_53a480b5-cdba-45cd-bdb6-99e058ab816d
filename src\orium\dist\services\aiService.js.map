{"version": 3, "file": "aiService.js", "sourceRoot": "", "sources": ["../../services/aiService.ts"], "names": [], "mappings": "AAAA;;;GAGG;AAEH,OAAO,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAC;AACxD,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAC;AAqCnD,MAAM,OAAO,SAAS;IACZ,SAAS,GAA4B,IAAI,GAAG,EAAE,CAAC;IAC/C,OAAO,GAAwB,IAAI,GAAG,EAAE,CAAC;IACzC,eAAe,GAAW,QAAQ,CAAC;IACnC,YAAY,GAAW,OAAO,CAAC;IAC/B,YAAY,GAAkB,IAAI,CAAC;IAE3C;QACE,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,MAAM,SAAS,GAAiB;YAC9B;gBACE,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,CAAC,OAAO,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,CAAC;gBAC3D,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,WAAW;gBACjB,EAAE,EAAE,WAAW;gBACf,MAAM,EAAE,CAAC,eAAe,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,UAAU,CAAC;gBAC1E,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,CAAC,YAAY,EAAE,mBAAmB,EAAE,QAAQ,CAAC;gBACrD,cAAc,EAAE,IAAI;aACrB;YACD;gBACE,IAAI,EAAE,QAAQ;gBACd,EAAE,EAAE,QAAQ;gBACZ,MAAM,EAAE,CAAC,SAAS,EAAE,eAAe,EAAE,iBAAiB,CAAC;gBACvD,cAAc,EAAE,IAAI;aACrB;SACF,CAAC;QAEF,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC3B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,QAAgB;QAC9B,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC;IAC/B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW;QACf,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,CAAC,UAAU,EAAE,CAAC;QACpE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,OAAO;QACT,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;gBAC7B,IAAI,CAAC;oBACH,MAAM,YAAY,GAAG,iBAAiB,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;oBAC9F,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;gBAClD,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,MAAM,CAAC,QAAQ,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU,CAAC,QAAgB,EAAE,MAAc;QAC/C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;QAClE,CAAC;QAED,MAAM,YAAY,GAAG,iBAAiB,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QAChF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,CAAC,UAAU,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAE3E,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,IAAI,KAAK,CAAC,2BAA2B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC;IAED;;OAEG;IACH,YAAY;QACV,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,UAAkB,EAAE,KAAc;QAC5C,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QAChD,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,KAAK,CAAC,qBAAqB,UAAU,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,eAAe,GAAG,UAAU,CAAC;QAClC,IAAI,KAAK,IAAI,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YAC7C,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC5B,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC;IACH,CAAC;IAED;;OAEG;IACH,kBAAkB;QAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAE,CAAC;QAC3D,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,YAAY,EAAE,CAAC;IAChD,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,UAAmB;QAC3B,MAAM,QAAQ,GAAG,UAAU,IAAI,IAAI,CAAC,eAAe,CAAC;QACpD,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,kBAAkB,CAAC,OAAkB;QACzC,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;QACtD,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,mCAAmC,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QAC7E,CAAC;QAED,QAAQ,IAAI,CAAC,eAAe,EAAE,CAAC;YAC7B,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC1C,KAAK,WAAW;gBACd,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC7C,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC1C,KAAK,QAAQ;gBACX,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAC1C;gBACE,MAAM,IAAI,KAAK,CAAC,yBAAyB,IAAI,CAAC,eAAe,EAAE,CAAC,CAAC;QACrE,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,uBAAuB,CAAC,IAAY,EAAE,cAAgD,EAAE,QAAgB;QAC5G,MAAM,MAAM,GAAG,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,cAAc,EAAE,QAAQ,CAAC,CAAC;QAE9E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAC7C,MAAM;YACN,QAAQ;YACR,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;IACrE,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,QAAgB;QAC9C,MAAM,MAAM,GAAG,yBAAyB,QAAQ,mCAAmC,QAAQ,KAAK,IAAI,UAAU,CAAC;QAE/G,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAC7C,MAAM;YACN,QAAQ;YACR,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,OAAO,CAAC,IAAY,EAAE,KAAa,EAAE,QAAgB;QACzD,MAAM,MAAM,GAAG,qBAAqB,QAAQ,+BAA+B,KAAK,cAAc,QAAQ,KAAK,IAAI,8CAA8C,CAAC;QAE9J,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAC7C,MAAM;YACN,QAAQ;YACR,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,IAAY,EAAE,QAAgB;QAChD,MAAM,MAAM,GAAG,uDAAuD,QAAQ,mBAAmB,QAAQ,KAAK,IAAI,UAAU,CAAC;QAE7H,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAC7C,MAAM;YACN,QAAQ;YACR,SAAS,EAAE,GAAG;YACd,WAAW,EAAE,GAAG;SACjB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,OAAO,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,OAAkB,EAAE,MAAc;QACzD,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,4CAA4C,EAAE;YACzE,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,eAAe,EAAE,UAAU,MAAM,EAAE;gBACnC,cAAc,EAAE,kBAAkB;aACnC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,KAAK,EAAE,IAAI,CAAC,YAAY;gBACxB,QAAQ,EAAE;oBACR,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,qCAAqC,EAAE;oBAClE,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE;iBAC1C;gBACD,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,GAAG;gBACpC,WAAW,EAAE,OAAO,CAAC,WAAW,IAAI,GAAG;aACxC,CAAC;SACH,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,qBAAqB,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnC,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO;YACxC,KAAK,EAAE,IAAI,CAAC,YAAY;YACxB,QAAQ,EAAE,QAAQ;YAClB,KAAK,EAAE;gBACL,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;gBACtC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB;gBAC9C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;aACrC;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,aAAa,CAAC,OAAkB,EAAE,MAAc;QAC5D,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,uCAAuC,EAAE;YACpE,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,WAAW,EAAE,MAAM;gBACnB,cAAc,EAAE,kBAAkB;gBAClC,mBAAmB,EAAE,YAAY;aAClC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,KAAK,EAAE,IAAI,CAAC,YAAY;gBACxB,UAAU,EAAE,OAAO,CAAC,SAAS,IAAI,GAAG;gBACpC,QAAQ,EAAE;oBACR,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE;iBAC1C;aACF,CAAC;SACH,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,wBAAwB,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACnC,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI;YAC7B,KAAK,EAAE,IAAI,CAAC,YAAY;YACxB,QAAQ,EAAE,WAAW;YACrB,KAAK,EAAE;gBACL,YAAY,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY;gBACrC,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,aAAa;gBAC1C,WAAW,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,aAAa;aAChE;SACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,OAAkB,EAAE,MAAc;QACzD,oCAAoC;QACpC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,OAAkB,EAAE,MAAc;QACzD,6BAA6B;QAC7B,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;IAChE,CAAC;IAED;;OAEG;IACK,yBAAyB,CAAC,IAAY,EAAE,cAAgD,EAAE,QAAgB;QAChH,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC/B,MAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;QACzD,MAAM,YAAY,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,MAAM,CAAC,CAAC;QACrE,MAAM,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAEjE,OAAO,0BAA0B,QAAQ,oDAAoD,YAAY,IAAI,WAAW,iBAAiB,IAAI,yCAAyC,CAAC;IACzL,CAAC;IAED;;OAEG;IACK,oBAAoB,CAAC,QAAgB,EAAE,cAAgD;QAC7F,mEAAmE;QACnE,MAAM,WAAW,GAAqB,EAAE,CAAC;QACzC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEnC,IAAI,iBAAiB,GAAG,EAAE,CAAC;QAC3B,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3B,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,iBAAiB,CAAC,IAAI,EAAE,EAAE,CAAC;wBAC7B,WAAW,CAAC,IAAI,CAAC;4BACf,IAAI,EAAE,iBAAiB,CAAC,IAAI,EAAE;4BAC9B,WAAW,EAAE,yBAAyB;4BACtC,UAAU,EAAE,GAAG;4BACf,SAAS,EAAE,cAAc,CAAC,IAAI;4BAC9B,OAAO,EAAE,cAAc,CAAC,IAAI;yBAC7B,CAAC,CAAC;oBACL,CAAC;oBACD,iBAAiB,GAAG,EAAE,CAAC;oBACvB,WAAW,GAAG,KAAK,CAAC;gBACtB,CAAC;qBAAM,CAAC;oBACN,WAAW,GAAG,IAAI,CAAC;gBACrB,CAAC;YACH,CAAC;iBAAM,IAAI,WAAW,EAAE,CAAC;gBACvB,iBAAiB,IAAI,IAAI,GAAG,IAAI,CAAC;YACnC,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,2BAA2B;IAC7D,CAAC;CACF;AAED,MAAM,CAAC,MAAM,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC"}