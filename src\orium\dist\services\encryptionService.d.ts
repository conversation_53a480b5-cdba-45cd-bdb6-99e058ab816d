/**
 * Encryption service for secure data handling in Orium
 * Uses AES-256-GCM for encryption and secure key derivation
 */
export declare class EncryptionService {
    private static readonly ALGORITHM;
    private static readonly KEY_SIZE;
    private static readonly IV_SIZE;
    private static readonly TAG_SIZE;
    private static readonly ITERATIONS;
    /**
     * Generate a random salt
     */
    private static generateSalt;
    /**
     * Generate a random IV
     */
    private static generateIV;
    /**
     * Derive key from password using PBKDF2
     */
    private static deriveKey;
    /**
     * Encrypt text using AES-256-GCM
     */
    static encrypt(plaintext: string, password: string): string;
    /**
     * Decrypt text using AES-256-GCM
     */
    static decrypt(encryptedData: string, password: string): string;
    /**
     * Generate a secure random password for session-based encryption
     */
    static generateSecurePassword(): string;
    /**
     * Hash password for secure storage (not for encryption)
     */
    static hashPassword(password: string): string;
    /**
     * Verify password against hash
     */
    static verifyPassword(password: string, hash: string): boolean;
    /**
     * Encrypt API key for secure storage
     */
    static encryptApiKey(apiKey: string, userPassword: string): string;
    /**
     * Decrypt API key from secure storage
     */
    static decryptApiKey(encryptedApiKey: string, userPassword: string): string;
    /**
     * Encrypt chat message for end-to-end encryption
     */
    static encryptChatMessage(message: string, sessionKey: string): string;
    /**
     * Decrypt chat message
     */
    static decryptChatMessage(encryptedMessage: string, sessionKey: string): string;
    /**
     * Generate a session key for chat encryption
     */
    static generateSessionKey(): string;
    /**
     * Encrypt file content for secure storage
     */
    static encryptFileContent(content: string, sessionKey: string): string;
    /**
     * Decrypt file content
     */
    static decryptFileContent(encryptedContent: string, sessionKey: string): string;
    /**
     * Create a secure hash of data (for integrity checking)
     */
    static createHash(data: string): string;
    /**
     * Verify data integrity using hash
     */
    static verifyHash(data: string, hash: string): boolean;
    /**
     * Generate a secure random token
     */
    static generateToken(length?: number): string;
    /**
     * Encrypt data with a public key (for key exchange)
     * Note: This is a simplified implementation. In production, use proper RSA encryption.
     */
    static encryptWithPublicKey(data: string, publicKey: string): string;
    /**
     * Decrypt data with a private key
     */
    static decryptWithPrivateKey(encryptedData: string, privateKey: string): string;
}
/**
 * Chat encryption manager for session-based encryption
 */
export declare class ChatEncryptionManager {
    private sessionKeys;
    /**
     * Initialize encryption for a session
     */
    initializeSession(sessionId: string): string;
    /**
     * Get session key
     */
    getSessionKey(sessionId: string): string | undefined;
    /**
     * Encrypt message for session
     */
    encryptMessage(sessionId: string, message: string): string;
    /**
     * Decrypt message for session
     */
    decryptMessage(sessionId: string, encryptedMessage: string): string;
    /**
     * Remove session encryption
     */
    removeSession(sessionId: string): void;
}
export declare const chatEncryptionManager: ChatEncryptionManager;
//# sourceMappingURL=encryptionService.d.ts.map